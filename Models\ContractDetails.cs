using System.Text.Json.Serialization;
using System.Collections.Generic;

namespace Excalibur.Models
{
    // Classe principal para encapsular a resposta completa
    public class ContractsForSymbol
    {
        [JsonPropertyName("available")]
        public List<ContractDetails> Available { get; set; } = new();
    }

    public class ContractDetails
    {
        [JsonPropertyName("contract_category_display")]
        public string CategoryDisplay { get; set; } = string.Empty;

        [JsonPropertyName("contract_display")]
        public string ContractDisplay { get; set; } = string.Empty;

        [JsonPropertyName("contract_type")]
        public string ContractType { get; set; } = string.Empty;

        [JsonPropertyName("min_contract_duration")]
        public string MinContractDuration { get; set; } = string.Empty;

        [JsonPropertyName("max_contract_duration")]
        public string MaxContractDuration { get; set; } = string.Empty;

        [JsonPropertyName("barriers")]
        public int? Barriers { get; set; } // Usando 'int?' para permitir nulo

        [JsonPropertyName("last_digit_range")]
        public List<int>? LastDigitRange { get; set; }
    }
}