Excelente, esta é exatamente a próxima etapa crucial para dar vida ao "Excalibur". Para que o usuário possa interagir com um contrato, a interface precisa se adaptar dinamicamente, mostrando apenas os campos necessários (duração, barreiras, dígito) para o contrato selecionado.
A fonte de toda essa informação é a resposta da chamada contracts_for_symbol. A documentação e a exploração da API nos mostram que, para cada tipo de contrato disponível para um símbolo, a API nos diz exatamente o que é necessário.
Vamos organizar os contratos e depois implementar a lógica para exibir os campos corretos.
Análise dos Tipos de Contrato e Seus Parâmetros
A API da Deriv agrupa os contratos por categorias. Dentro de cada categoria, encontramos as durações permitidas, a quantidade de barreiras e outras informações.
Aqui está a organização dos contratos mais comuns, baseada na estrutura da resposta da API:
1. Contratos Baseados Apenas em Duração (Sem Barreiras / Dígitos)
Estes são os contratos mais simples, onde o resultado depende apenas do movimento do preço em relação ao ponto de entrada após um certo tempo.
Exemplos Comuns:
Sobe/Desce (Rise/Fall): Você prevê se o preço de saída será estritamente superior (Sobe/CALL) ou inferior (Desce/PUT) ao preço de entrada.
Asiáticos (Asians): O resultado depende da média dos ticks (Asiático Sobe/ASIANU ou Asiático Desce/ASIAND).
Como Identificar na API:
Na resposta de contracts_for, esses contratos NÃO terão as chaves barriers, barrier_category, ou last_digit_range.
Eles terão as chaves min_contract_duration e max_contract_duration.
Parâmetros Exigidos para Proposta:
duration: Um número.
duration_unit: A unidade de tempo (t para ticks, s para segundos, m para minutos, h para horas, d para dias).
2. Contratos Baseados em Barreiras (1 ou 2 Barreiras)
O resultado aqui depende do preço cruzar (ou não) um ou dois níveis de preço pré-definidos (barreiras).
Exemplos Comuns:
Acima/Abaixo (Higher/Lower): Você prevê se o preço final estará acima (HIGHER) ou abaixo (LOWER) de uma barreira. (Requer 1 barreira).
Toca/Não Toca (Touch/No Touch): Você prevê se o preço irá tocar (TOUCH) ou não (NOTOUCH) uma barreira a qualquer momento durante o contrato. (Requer 1 barreira).
Dentro/Fora (Stays In/Goes Out): Você prevê se o preço permanecerá dentro (EXPIRYRANGE) ou sairá (EXPIRYMISS) de um intervalo definido por duas barreiras. (Requer 2 barreiras).
Como Identificar na API:
A chave barriers terá o valor 1 ou 2, indicando o número de barreiras necessárias.
A chave barrier_category indicará se a barreira é "europeia" (verificada apenas no final) ou "americana" (verificada durante todo o período).
Parâmetros Exigidos para Proposta:
duration e duration_unit.
barrier: O valor da barreira principal (relativo ou absoluto).
barrier2: O valor da segunda barreira (para contratos como Dentro/Fora).
3. Contratos Baseados em Dígitos (Último Dígito do Preço)
Estes contratos dependem exclusivamente do último dígito do preço do ativo após um número específico de ticks.
Exemplos Comuns:
Combina/Diferente (Matches/Differs): Você prevê se o último dígito do preço irá combinar (DIGITMATCH) ou ser diferente (DIGITDIFF) de um dígito que você escolher.
Par/Ímpar (Even/Odd): Você prevê se o último dígito será par (DIGITEVEN) ou ímpar (DIGITODD).
Acima/Abaixo (Over/Under): Você prevê se o último dígito será acima (DIGITOVER) ou abaixo (DIGITUNDER) de um dígito que você escolher.
Como Identificar na API:
A chave last_digit_range estará presente e conterá um array com os dígitos possíveis (ex: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]).
A duração é quase sempre em ticks.
Parâmetros Exigidos para Proposta:
duration e duration_unit (geralmente t).
last_digit: O dígito de 0 a 9 que você está prevendo (para Combina/Diferente, Acima/Abaixo).
Passo 1: Atualizar os Models para Conter os Detalhes
Precisamos de um modelo mais rico que o ContractCategory para armazenar essas regras.
Models/ContractDetails.cs
code
C#
using System.Text.Json.Serialization;
using System.Collections.Generic;

namespace Excalibur.Models
{
    // Classe principal para encapsular a resposta completa
    public class ContractsForSymbol
    {
        [JsonPropertyName("available")]
        public List<ContractDetails> Available { get; set; }
    }

    public class ContractDetails
    {
        [JsonPropertyName("contract_category_display")]
        public string CategoryDisplay { get; set; }

        [JsonPropertyName("contract_display")]
        public string ContractDisplay { get; set; }

        [JsonPropertyName("contract_type")]
        public string ContractType { get; set; }

        [JsonPropertyName("min_contract_duration")]
        public string MinContractDuration { get; set; }

        [JsonPropertyName("max_contract_duration")]
        public string MaxContractDuration { get; set; }

        [JsonPropertyName("barriers")]
        public int? Barriers { get; set; } // Usando 'int?' para permitir nulo

        [JsonPropertyName("last_digit_range")]
        public List<int> LastDigitRange { get; set; }
    }
}
Passo 2: Atualizar o Serviço da API
Modifique DerivApiService para usar o novo modelo.
IDerivApiService.cs (altere a assinatura do método)
code
C#
// ...
Task<ContractsForSymbol> GetContractsForSymbolAsync(string symbol);
DerivApiService.cs (altere a implementação)
code
C#
public async Task<ContractsForSymbol> GetContractsForSymbolAsync(string symbol)
{
    var request = new { contracts_for = symbol };
    var response = await SendRequestAsync(request);
    
    // A propriedade raiz é "contracts_for"
    var contractsData = response.GetProperty("contracts_for").Deserialize<ContractsForSymbol>();
    return contractsData;
}
Passo 3: Atualizar o MainViewModel para Expor a Lógica
O ViewModel agora precisa ter propriedades que a UI possa usar para mostrar/esconder os campos de entrada corretos.
MainViewModel.cs
code
C#
// using Excalibur.Models;
// using System.Collections.ObjectModel;

// Altere a coleção e a propriedade selecionada para usar o novo modelo
public ObservableCollection<ContractDetails> ContractTypes { get; } = new();

private ContractDetails _selectedContractType;
public ContractDetails SelectedContractType
{
    get => _selectedContractType;
    set
    {
        _selectedContractType = value;
        OnPropertyChanged();
        UpdateContractParameters(); // Método chave para atualizar a UI
    }
}

// Novas propriedades para controlar a visibilidade da UI
private bool _isDurationVisible;
public bool IsDurationVisible { get => _isDurationVisible; set { _isDurationVisible = value; OnPropertyChanged(); } }

private bool _isBarrier1Visible;
public bool IsBarrier1Visible { get => _isBarrier1Visible; set { _isBarrier1Visible = value; OnPropertyChanged(); } }

private bool _isBarrier2Visible;
public bool IsBarrier2Visible { get => _isBarrier2Visible; set { _isBarrier2Visible = value; OnPropertyChanged(); } }

private bool _isDigitSelectionVisible;
public bool IsDigitSelectionVisible { get => _isDigitSelectionVisible; set { _isDigitSelectionVisible = value; OnPropertyChanged(); } }

private string _durationInfo;
public string DurationInfo { get => _durationInfo; set { _durationInfo = value; OnPropertyChanged(); } }


// Lógica para atualizar a UI com base no contrato selecionado
private void UpdateContractParameters()
{
    if (SelectedContractType == null)
    {
        IsDurationVisible = false;
        IsBarrier1Visible = false;
        IsBarrier2Visible = false;
        IsDigitSelectionVisible = false;
        DurationInfo = string.Empty;
        return;
    }

    // Reset
    IsBarrier1Visible = false;
    IsBarrier2Visible = false;
    IsDigitSelectionVisible = false;

    // Sempre mostrar duração
    IsDurationVisible = true;
    DurationInfo = $"Duração: Min {SelectedContractType.MinContractDuration}, Max {SelectedContractType.MaxContractDuration}";

    // Verificar Barreiras
    if (SelectedContractType.Barriers.HasValue)
    {
        if (SelectedContractType.Barriers >= 1) IsBarrier1Visible = true;
        if (SelectedContractType.Barriers == 2) IsBarrier2Visible = true;
    }

    // Verificar Dígitos
    if (SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
    {
        IsDigitSelectionVisible = true;
    }
}


// Atualize o método que busca os contratos
private async void UpdateContractTypes()
{
    ContractTypes.Clear();
    SelectedContractType = null; // Isso vai chamar o UpdateContractParameters e limpar a UI
    
    if (SelectedAsset == null) return;

    try
    {
        var contractsResponse = await _derivApiService.GetContractsForSymbolAsync(SelectedAsset.Symbol);
        foreach (var contract in contractsResponse.Available.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
        {
            ContractTypes.Add(contract);
        }
    }
    catch (Exception ex)
    {
        // Tratar erro
    }
}
Passo 4: Criar a View Dinâmica com XAML
Agora, criamos um novo card para os parâmetros do contrato. Usaremos Binding na propriedade Visibility para mostrar/esconder os campos. BooleanToVisibilityConverter é nativo do WPF, então não precisamos criar um.
MainWindow.xaml (adicione um novo CardControl)
code
Xml
<!-- No Grid principal, adicione este card -->
<ui:CardControl Header="Parâmetros do Contrato"
                Margin="20"
                Grid.Row="1" <!-- Ou onde preferir -->
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Width="380">
    <StackPanel Margin="10">
        <StackPanel.Resources>
            <BooleanToVisibilityConverter x:Key="BoolToVis"/>
        </StackPanel.Resources>
        
        <!-- Campo de Duração -->
        <StackPanel Visibility="{Binding IsDurationVisible, Converter={StaticResource BoolToVis}}">
            <TextBlock Text="Duração" FontWeight="Bold"/>
            <TextBlock Text="{Binding DurationInfo}" FontSize="11" Opacity="0.8" Margin="0,2,0,5"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <ui:NumberBox PlaceholderText="Duração" Margin="0,0,5,0"/>
                <ui:ComboBox Grid.Column="1" PlaceholderText="Unidade" MinWidth="80">
                    <ComboBoxItem Content="Ticks"/>
                    <ComboBoxItem Content="Segundos"/>
                    <ComboBoxItem Content="Minutos"/>
                    <ComboBoxItem Content="Horas"/>
                    <ComboBoxItem Content="Dias"/>
                </ui:ComboBox>
            </Grid>
        </StackPanel>

        <!-- Campo de Barreira 1 -->
        <StackPanel Margin="0,10,0,0" Visibility="{Binding IsBarrier1Visible, Converter={StaticResource BoolToVis}}">
            <TextBlock Text="Barreira 1" FontWeight="Bold"/>
            <ui:TextBox PlaceholderText="Ex: +10.5 ou 158.2"/>
        </StackPanel>
        
        <!-- Campo de Barreira 2 -->
        <StackPanel Margin="0,10,0,0" Visibility="{Binding IsBarrier2Visible, Converter={StaticResource BoolToVis}}">
            <TextBlock Text="Barreira 2" FontWeight="Bold"/>
            <ui:TextBox PlaceholderText="Ex: -10.5 ou 140.1"/>
        </StackPanel>
        
        <!-- Campo de Seleção de Dígito -->
        <StackPanel Margin="0,10,0,0" Visibility="{Binding IsDigitSelectionVisible, Converter={StaticResource BoolToVis}}">
            <TextBlock Text="Previsão do Último Dígito" FontWeight="Bold"/>
            <ui:ComboBox PlaceholderText="Selecione um dígito...">
                <!-- No mundo real, isso seria populado dinamicamente -->
                <ComboBoxItem Content="0"/>
                <ComboBoxItem Content="1"/>
                <ComboBoxItem Content="2"/>
                <ComboBoxItem Content="3"/>
                <ComboBoxItem Content="4"/>
                <ComboBoxItem Content="5"/>
                <ComboBoxItem Content="6"/>
                <ComboBoxItem Content="7"/>
                <ComboBoxItem Content="8"/>
                <ComboBoxItem Content="9"/>
            </ui:ComboBox>
        </StackPanel>
        
        <Separator Margin="0,15,0,10"/>
        
        <Button Content="Calcular Payout" Command="{Binding CalculatePayoutCommand}" Style="{StaticResource UiButtonSuccessAppearance}"/>
    </StackPanel>
</ui:CardControl>
Como Fazer a Chamada de Proposta (proposal)
Com esta estrutura, quando o usuário clicar no botão "Calcular Payout", seu ICommand no ViewModel terá acesso a todas as informações necessárias:
SelectedContractType.ContractType (ex: "CALL", "DIGITMATCH")
O valor do TextBox de duração.
A unidade de duração selecionada.
Os valores das barreiras (se visíveis).
O dígito selecionado (se visível).
Você então montará o objeto de requisição para a chamada proposal da API, que retornará o payout e o preço de compra.
Esta abordagem torna a UI extremamente flexível e à prova de futuro. Se a Deriv adicionar um novo tipo de contrato com regras diferentes, você só precisará ajustar a lógica no UpdateContractParameters do ViewModel, sem precisar redesenhar a interface.