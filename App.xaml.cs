using Excalibur.Services;
using Excalibur.ViewModels;
using Excalibur.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.IO;
using System.Windows;
using MainWindow = Excalibur.Views.MainWindow;

namespace Excalibur;

public partial class App : Application
{
    private static readonly IHost _host = Host
        .CreateDefaultBuilder()
        .UseSerilog((context, config) =>
        {
            // Criar pasta de logs se não existir
            Directory.CreateDirectory("logs");
            config.WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day);
        })
        .ConfigureServices((context, services) =>
        {
            // Registrar Logging
            services.AddLogging();

            // Registrar Serviços
            services.AddSingleton<IDerivApiService, DerivApiService>();

            // Registrar ViewModels
            services.AddSingleton<MainViewModel>();

            // Registrar a Janela Principal
            services.AddSingleton<MainWindow>();
        })
        .Build();

    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            await _host.StartAsync();

            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.DataContext = _host.Services.GetRequiredService<MainViewModel>();
            mainWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Erro ao iniciar a aplicação: {ex.Message}", "Erro", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        try
        {
            await _host.StopAsync();
        }
        catch
        {
            // Ignorar erros ao parar o host
        }
        base.OnExit(e);
    }
}

