Consultar a API para obter a lista de todos os "símbolos ativos" (active_symbols), que nos dará a hierarquia de mercados, submercados e ativos negociáveis.
Consultar a API novamente para obter os tipos de contrato disponíveis para um símbolo específico (contracts_for_symbol) quando o usuário o selecionar.
Modelar os dados em C# para facilitar o manuseio.
Atualizar o ViewModel para gerenciar a lógica de seleção em cascata (a seleção de um mercado filtra os submercados, etc.).
Criar a View (XAML) com componentes do WPF UI para apresentar os dados de forma clara e profissional.
Passo 1: Atualizar o Serviço da API (DerivApiService)
Primeiro, precisamos de métodos para buscar os dados de mercados e contratos. O padrão ideal para lidar com requisições e respostas em um WebSocket é usar um TaskCompletionSource, que nos permite "esperar" (await) por uma resposta que chegará em um momento futuro.
IDerivApiService.cs (adicione os novos métodos)
code
C#
// ... (métodos existentes)
Task<List<ActiveSymbol>> GetActiveSymbolsAsync();
Task<List<ContractCategory>> GetContractsForSymbolAsync(string symbol);
DerivApiService.cs (adicione as implementações)
code
C#
// ... (no topo da classe)
using System.Collections.Concurrent;
using System.Text.Json.Serialization; // Para JsonPropertyName

// Dicionário para rastrear requisições e suas respostas
private readonly ConcurrentDictionary<int, TaskCompletionSource<JsonElement>> _pendingRequests = new();
private int _nextRequestId = 1;

// ... (no construtor ou onde o _ws é inicializado, modifique o MessageReceived)
_ws.MessageReceived.Subscribe(msg => {
    // Log da mensagem
    _logger.LogInformation($"Mensagem recebida: {msg.Text}");

    // Novo processador de mensagens
    ProcessGeneralMessage(msg.Text);
});


// Novo método para processar QUALQUER mensagem da API
private void ProcessGeneralMessage(string jsonMessage)
{
    try
    {
        using var doc = JsonDocument.Parse(jsonMessage);
        var root = doc.RootElement;

        // Verifica se a mensagem é uma resposta a uma de nossas requisições
        if (root.TryGetProperty("req_id", out var reqIdElement) && 
            _pendingRequests.TryRemove(reqIdElement.GetInt32(), out var tcs))
        {
            // Se for, completa a Task com o resultado
            if (root.TryGetProperty("error", out var error))
            {
                tcs.SetException(new Exception(error.GetProperty("message").GetString()));
            }
            else
            {
                tcs.SetResult(root.Clone());
            }
            return;
        }

        // Processa mensagens que não são respostas diretas (streams, etc.)
        ProcessMessage(jsonMessage); // Chamamos o método antigo para compatibilidade
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Erro ao processar mensagem geral da API.");
    }
}


// Método genérico para enviar uma requisição e aguardar a resposta
private async Task<JsonElement> SendRequestAsync<T>(T request)
{
    var requestId = Interlocked.Increment(ref _nextRequestId);
    var tcs = new TaskCompletionSource<JsonElement>();
    _pendingRequests[requestId] = tcs;

    // Adiciona o req_id ao objeto de requisição
    var jsonRequest = JsonDocument.Parse(JsonSerializer.Serialize(request)).RootElement;
    var finalRequest = new Dictionary<string, object>();
    foreach (var property in jsonRequest.EnumerateObject())
    {
        finalRequest[property.Name] = property.Value;
    }
    finalRequest["req_id"] = requestId;

    _ws.Send(JsonSerializer.Serialize(finalRequest));

    // Aguarda a resposta ou um timeout
    var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(10000));
    if (completedTask != tcs.Task)
    {
        _pendingRequests.TryRemove(requestId, out _);
        throw new TimeoutException("A requisição para a API Deriv expirou.");
    }
    
    return await tcs.Task;
}


// Implementação dos novos métodos da interface
public async Task<List<ActiveSymbol>> GetActiveSymbolsAsync()
{
    var request = new { active_symbols = "full", product_type = "basic" };
    var response = await SendRequestAsync(request);
    var symbols = response.GetProperty("active_symbols").Deserialize<List<ActiveSymbol>>();
    return symbols;
}

public async Task<List<ContractCategory>> GetContractsForSymbolAsync(string symbol)
{
    var request = new { contracts_for = symbol };
    var response = await SendRequestAsync(request);
    var contracts = response.GetProperty("contracts_for").GetProperty("available").Deserialize<List<ContractCategory>>();
    return contracts;
}
Passo 2: Criar os Models para os Dados
Na pasta Models, crie estes dois arquivos. Eles representarão os dados que recebemos da API.
ActiveSymbol.cs
code
C#
using System.Text.Json.Serialization;

namespace Excalibur.Models;

public class ActiveSymbol
{
    [JsonPropertyName("display_name")]
    public string DisplayName { get; set; }

    [JsonPropertyName("market")]
    public string Market { get; set; }

    [JsonPropertyName("market_display_name")]
    public string MarketDisplayName { get; set; }

    [JsonPropertyName("submarket")]
    public string Submarket { get; set; }

    [JsonPropertyName("submarket_display_name")]
    public string SubmarketDisplayName { get; set; }

    [JsonPropertyName("symbol")]
    public string Symbol { get; set; }
}
ContractCategory.cs
code
C#
using System.Text.Json.Serialization;

namespace Excalibur.Models;

public class ContractCategory
{
    [JsonPropertyName("contract_category_display")]
    public string CategoryDisplay { get; set; }

    [JsonPropertyName("contract_display")]
    public string ContractDisplay { get; set; }

    [JsonPropertyName("contract_type")]
    public string ContractType { get; set; }
}
Passo 3: Atualizar o MainViewModel com a Lógica de Seleção
Vamos adicionar as coleções de dados e as propriedades para os itens selecionados, implementando a lógica de cascata.
MainViewModel.cs
code
C#
// Adicione os using
using Excalibur.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

// ... dentro da classe MainViewModel

// Coleções para os ComboBoxes
public ObservableCollection<string> Markets { get; } = new();
public ObservableCollection<string> Submarkets { get; } = new();
public ObservableCollection<ActiveSymbol> Assets { get; } = new();
public ObservableCollection<ContractCategory> ContractTypes { get; } = new();

// Lista completa de símbolos, para não precisar buscar toda hora
private List<ActiveSymbol> _allActiveSymbols = new();

// Propriedades para os itens selecionados
private string _selectedMarket;
public string SelectedMarket
{
    get => _selectedMarket;
    set
    {
        _selectedMarket = value;
        OnPropertyChanged();
        UpdateSubmarkets();
    }
}

private string _selectedSubmarket;
public string SelectedSubmarket
{
    get => _selectedSubmarket;
    set
    {
        _selectedSubmarket = value;
        OnPropertyChanged();
        UpdateAssets();
    }
}

private ActiveSymbol _selectedAsset;
public ActiveSymbol SelectedAsset
{
    get => _selectedAsset;
    set
    {
        _selectedAsset = value;
        OnPropertyChanged();
        UpdateContractTypes();
    }
}

private ContractCategory _selectedContractType;
public ContractCategory SelectedContractType
{
    get => _selectedContractType;
    set
    {
        _selectedContractType = value;
        OnPropertyChanged();
        // Lógica futura ao selecionar um contrato
    }
}


// Modifique o construtor
public MainViewModel(IDerivApiService derivApiService)
{
    _derivApiService = derivApiService;
    SubscribeToApiEvents();
    
    // Conecta e, se tiver sucesso, carrega os ativos
    _derivApiService.ConnectionEstablished += async () => {
        Application.Current.Dispatcher.Invoke(() => IsConnected = true);
        await LoadTradingAssets();
    };

    _derivApiService.ConnectionLost += () => {
        Application.Current.Dispatcher.Invoke(() => IsConnected = false);
    };

    _derivApiService.AccountInfoUpdated += OnAccountInfoUpdated;
    _derivApiService.PingUpdated += OnPingUpdated;
    
    _derivApiService.ConnectAndAuthorizeAsync();
}

// Métodos para carregar e filtrar os dados
private async Task LoadTradingAssets()
{
    try
    {
        _allActiveSymbols = await _derivApiService.GetActiveSymbolsAsync();
        
        // Agrupa por mercado e preenche o primeiro ComboBox
        var markets = _allActiveSymbols
            .Select(s => s.MarketDisplayName)
            .Distinct()
            .OrderBy(m => m);
        
        Application.Current.Dispatcher.Invoke(() =>
        {
            Markets.Clear();
            foreach (var market in markets)
            {
                Markets.Add(market);
            }
        });
    }
    catch (Exception ex)
    {
        // Tratar erro (ex: mostrar uma mensagem na UI)
    }
}

private void UpdateSubmarkets()
{
    Submarkets.Clear();
    Assets.Clear();
    ContractTypes.Clear();
    SelectedSubmarket = null;

    if (SelectedMarket == null) return;

    var submarkets = _allActiveSymbols
        .Where(s => s.MarketDisplayName == SelectedMarket)
        .Select(s => s.SubmarketDisplayName)
        .Distinct()
        .OrderBy(sm => sm);

    foreach (var submarket in submarkets)
    {
        Submarkets.Add(submarket);
    }
}

private void UpdateAssets()
{
    Assets.Clear();
    ContractTypes.Clear();
    SelectedAsset = null;

    if (SelectedSubmarket == null) return;

    var assets = _allActiveSymbols
        .Where(s => s.SubmarketDisplayName == SelectedSubmarket)
        .OrderBy(s => s.DisplayName);

    foreach (var asset in assets)
    {
        Assets.Add(asset);
    }
}

private async void UpdateContractTypes()
{
    ContractTypes.Clear();
    SelectedContractType = null;
    
    if (SelectedAsset == null) return;

    try
    {
        var contracts = await _derivApiService.GetContractsForSymbolAsync(SelectedAsset.Symbol);
        foreach (var contract in contracts.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
        {
            ContractTypes.Add(contract);
        }
    }
    catch (Exception ex)
    {
        // Tratar erro
    }
}
Passo 4: Construir a View (MainWindow.xaml)
Agora, vamos adicionar o novo card à nossa janela principal. Ele terá 4 ComboBoxes que serão habilitados em sequência, guiando o usuário.
MainWindow.xaml (adicione este CardControl dentro do Grid principal, talvez ao lado ou abaixo do card de status)
code
Xml
<!-- ... (Grid e CardControl existentes) ... -->

<ui:CardControl Header="Seleção de Contrato"
                Margin="20"
                HorizontalAlignment="Right"
                VerticalAlignment="Top"
                Width="380">
    <StackPanel Margin="10">
        <!-- 1. Seleção de Mercado -->
        <TextBlock Text="Mercado" FontWeight="Bold" Margin="0,0,0,5"/>
        <ui:ComboBox x:Name="MarketComboBox"
                     ItemsSource="{Binding Markets}"
                     SelectedItem="{Binding SelectedMarket}"
                     PlaceholderText="Selecione um mercado..." />

        <!-- 2. Seleção de Submercado -->
        <TextBlock Text="Submercado" FontWeight="Bold" Margin="0,15,0,5"/>
        <ui:ComboBox x:Name="SubmarketComboBox"
                     ItemsSource="{Binding Submarkets}"
                     SelectedItem="{Binding SelectedSubmarket}"
                     IsEnabled="{Binding ElementName=MarketComboBox, Path=HasItems}"
                     PlaceholderText="Selecione um submercado..." />

        <!-- 3. Seleção de Ativo -->
        <TextBlock Text="Ativo" FontWeight="Bold" Margin="0,15,0,5"/>
        <ui:ComboBox x:Name="AssetComboBox"
                     ItemsSource="{Binding Assets}"
                     SelectedItem="{Binding SelectedAsset}"
                     DisplayMemberPath="DisplayName"
                     IsEnabled="{Binding ElementName=SubmarketComboBox, Path=HasItems}"
                     PlaceholderText="Selecione um ativo..." />
        
        <!-- 4. Seleção de Tipo de Contrato -->
        <TextBlock Text="Tipo de Contrato" FontWeight="Bold" Margin="0,15,0,5"/>
        <ui:ComboBox x:Name="ContractComboBox"
                     ItemsSource="{Binding ContractTypes}"
                     SelectedItem="{Binding SelectedContractType}"
                     IsEnabled="{Binding ElementName=AssetComboBox, Path=SelectedItem, Converter={StaticResource IsNotNullConverter}}"
                     PlaceholderText="Selecione um tipo de contrato...">
            <ui:ComboBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel>
                        <TextBlock Text="{Binding CategoryDisplay}" FontWeight="SemiBold"/>
                        <TextBlock Text="{Binding ContractDisplay}" FontSize="11" Opacity="0.8"/>
                    </StackPanel>
                </DataTemplate>
            </ui:ComboBox.ItemTemplate>
        </ui:ComboBox>
    </StackPanel>
</ui:CardControl>

<!-- Para o IsNotNullConverter funcionar, adicione isso no Window.Resources -->
<Window.Resources>
    <!-- ... (seu storyboard existente) ... -->
    <local:IsNotNullConverter x:Key="IsNotNullConverter"/>
</Window.Resources>
Para usar o IsNotNullConverter, você precisa criá-lo. Crie uma pasta Infrastructure no seu projeto e adicione o arquivo IsNotNullConverter.cs:
IsNotNullConverter.cs
code
C#
using System;
using System.Globalization;
using System.Windows.Data;

namespace Excalibur.Infrastructure;

public class IsNotNullConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value != null;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
E não se esqueça de adicionar o namespace no seu MainWindow.xaml:
xmlns:local="clr-namespace:Excalibur.Infrastructure"
Resultado Visual e Funcional
Ao executar a aplicação:
Assim que a conexão for estabelecida, o primeiro ComboBox ("Mercado") será preenchido com opções como "Sintéticos", "Forex", "Criptomoedas", etc.
Quando você selecionar um mercado (ex: "Forex"), o ComboBox "Submercado" será habilitado e preenchido com opções como "Pares Maiores", "Pares Menores".
Ao selecionar um submercado, o ComboBox "Ativo" será preenchido com os ativos correspondentes (ex: "EUR/USD", "GBP/USD").
Finalmente, ao selecionar um ativo, a aplicação fará uma nova chamada à API para buscar os contratos disponíveis para aquele ativo, preenchendo o último ComboBox com opções como "Sobe/Desce", "Acima/Abaixo", etc., de forma bem organizada.
Este design cria uma experiência de usuário guiada, limpa e profissional, exatamente como solicitado.