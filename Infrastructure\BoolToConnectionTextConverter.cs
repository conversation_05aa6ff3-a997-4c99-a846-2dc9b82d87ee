using System;
using System.Globalization;
using System.Windows.Data;

namespace Excalibur.Infrastructure;

public class BoolToConnectionTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isConnected)
        {
            return isConnected ? "Conectado" : "Desconectado";
        }
        return "Desconectado";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}