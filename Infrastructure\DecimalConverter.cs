using System;
using System.Globalization;
using System.Windows.Data;

namespace Excalibur.Infrastructure
{
    public class DecimalConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue.ToString("F2", CultureInfo.InvariantCulture);
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                // Substitui vírgula por ponto para garantir compatibilidade
                stringValue = stringValue.Replace(',', '.');
                
                if (decimal.TryParse(stringValue, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal result))
                {
                    return result;
                }
            }
            return 0m;
        }
    }
}