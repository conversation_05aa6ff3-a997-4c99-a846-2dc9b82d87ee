using System.Text.Json.Serialization;

namespace Excalibur.Models;

public class BuyResponse
{
    [JsonPropertyName("buy")]
    public BuyContract Buy { get; set; }

    [JsonPropertyName("error")]
    public ApiError Error { get; set; }
}

public class ApiError
{
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;
    
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
}

public class BuyContract
{
    [JsonPropertyName("contract_id")]
    public long ContractId { get; set; }

    [JsonPropertyName("longcode")]
    public string LongCode { get; set; }

    [JsonPropertyName("start_time")]
    public long StartTime { get; set; }

    [JsonPropertyName("transaction_id")]
    public long TransactionId { get; set; }

    [JsonPropertyName("balance_after")]
    public decimal BalanceAfter { get; set; }

    [JsonPropertyName("buy_price")]
    public decimal BuyPrice { get; set; }

    [Json<PERSON>ropertyName("payout")]
    public decimal Payout { get; set; }

    [JsonPropertyName("purchase_time")]
    public long PurchaseTime { get; set; }

    [JsonPropertyName("shortcode")]
    public string ShortCode { get; set; }
}