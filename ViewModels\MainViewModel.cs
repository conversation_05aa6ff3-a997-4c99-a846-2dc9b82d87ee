using Excalibur.Services;
using Excalibur.Models;
using Excalibur.Infrastructure;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class MainViewModel : ObservableObject
{
    private readonly IDerivApiService _derivApiService;
    private readonly ILogger<MainViewModel> _logger;

    private bool _isConnected;
    public bool IsConnected
    {
        get => _isConnected;
        set { _isConnected = value; OnPropertyChanged(); }
    }

    private string _accountCode = "-----------";
    public string AccountCode
    {
        get => _accountCode;
        set { _accountCode = value; OnPropertyChanged(); }
    }
    
    private string _accountType = "---";
    public string AccountType
    {
        get => _accountType;
        set { _accountType = value; OnPropertyChanged(); }
    }

    private double _balance;
    public double Balance
    {
        get => _balance;
        set { _balance = value; OnPropertyChanged(); }
    }

    private long _ping;
    public long Ping
    {
        get => _ping;
        set { _ping = value; OnPropertyChanged(); }
    }

    // Coleções para seleção de contratos
    private ObservableCollection<string> _markets = new();
    public ObservableCollection<string> Markets
    {
        get => _markets;
        set { _markets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<string> _subMarkets = new();
    public ObservableCollection<string> SubMarkets
    {
        get => _subMarkets;
        set { _subMarkets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ActiveSymbol> _activeSymbols = new();
    public ObservableCollection<ActiveSymbol> ActiveSymbols
    {
        get => _activeSymbols;
        set { _activeSymbols = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ContractDetails> _contractTypes = new();
    public ObservableCollection<ContractDetails> ContractTypes
    {
        get => _contractTypes;
        set { _contractTypes = value; OnPropertyChanged(); }
    }

    // Propriedades para seleções atuais
    private string _selectedMarket;
    public string SelectedMarket
    {
        get => _selectedMarket;
        set
        {
            _selectedMarket = value;
            OnPropertyChanged();
            OnMarketSelectionChanged();
        }
    }

    private decimal CalculateNextMartingaleStake()
    {
        if (InitialStakeAmount == 0 && decimal.TryParse(StakeAmount, out decimal initialStake))
        {
            InitialStakeAmount = initialStake;
        }

        var nextLevel = CurrentMartingaleLevel + 1;
        if (nextLevel == 1)
        {
            return InitialStakeAmount;
        }
        else
        {
            return InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, nextLevel - 1);
        }
    }

    private string _selectedSubMarket;
    public string SelectedSubMarket
    {
        get => _selectedSubMarket;
        set
        {
            _selectedSubMarket = value;
            OnPropertyChanged();
            OnSubMarketSelectionChanged();
        }
    }

    private ActiveSymbol _selectedActiveSymbol;
    public ActiveSymbol SelectedActiveSymbol
    {
        get => _selectedActiveSymbol;
        set
        {
            _selectedActiveSymbol = value;
            OnPropertyChanged();
            OnActiveSymbolSelectionChanged();
        }
    }

    private ContractDetails _selectedContractType;
    public ContractDetails SelectedContractType
    {
        get => _selectedContractType;
        set
        {
            _selectedContractType = value;
            OnPropertyChanged();
            UpdateContractParameters(); // Método chave para atualizar a UI
        }
    }

    // Novas propriedades para controlar a visibilidade da UI
    private bool _isDurationVisible;
    public bool IsDurationVisible { get => _isDurationVisible; set { _isDurationVisible = value; OnPropertyChanged(); } }

    private bool _isBarrier1Visible;
    public bool IsBarrier1Visible { get => _isBarrier1Visible; set { _isBarrier1Visible = value; OnPropertyChanged(); } }

    private bool _isBarrier2Visible;
    public bool IsBarrier2Visible { get => _isBarrier2Visible; set { _isBarrier2Visible = value; OnPropertyChanged(); } }

    private bool _isDigitSelectionVisible;
    public bool IsDigitSelectionVisible { get => _isDigitSelectionVisible; set { _isDigitSelectionVisible = value; OnPropertyChanged(); } }

    private string _durationInfo = string.Empty;
    public string DurationInfo { get => _durationInfo; set { _durationInfo = value; OnPropertyChanged(); } }

    // Propriedades para Stake e cálculo de payout
    private decimal _stake = 10.0m;
    public decimal Stake 
    { 
        get => _stake; 
        set 
        { 
            _stake = Math.Round(value, 2); 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    // StakeAmount property moved to end of class to integrate with Martingale

    private string _barrier1Value = string.Empty;
    public string Barrier1Value 
    { 
        get => _barrier1Value; 
        set 
        { 
            _barrier1Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _barrier2Value = string.Empty;
    public string Barrier2Value 
    { 
        get => _barrier2Value; 
        set 
        { 
            _barrier2Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _durationValue = 5;
    public int DurationValue 
    { 
        get => _durationValue; 
        set 
        { 
            _durationValue = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _durationUnit = "t";
    public string DurationUnit 
    { 
        get => _durationUnit; 
        set 
        { 
            _durationUnit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _selectedDigit = 0;
    public int SelectedDigit 
    { 
        get => _selectedDigit; 
        set 
        { 
            _selectedDigit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    // Propriedades para exibir resultados do cálculo
    private decimal _calculatedPayout;
    public decimal CalculatedPayout { get => _calculatedPayout; set { _calculatedPayout = value; OnPropertyChanged(); } }

    private decimal _askPrice;
    public decimal AskPrice
    {
        get => _askPrice;
        set { _askPrice = value; OnPropertyChanged(); }
    }

    private string _currentProposalId;
    public string CurrentProposalId
    {
        get => _currentProposalId;
        set { _currentProposalId = value; OnPropertyChanged(); }
    }

    private string _calculatedBarrier1 = string.Empty;
    public string CalculatedBarrier1 { get => _calculatedBarrier1; set { _calculatedBarrier1 = value; OnPropertyChanged(); } }

    private string _calculatedBarrier2 = string.Empty;
    public string CalculatedBarrier2 { get => _calculatedBarrier2; set { _calculatedBarrier2 = value; OnPropertyChanged(); } }

    private string _barrier1Suggestion = string.Empty;
    public string Barrier1Suggestion { get => _barrier1Suggestion; set { _barrier1Suggestion = value; OnPropertyChanged(); } }

    private string _barrier2Suggestion = string.Empty;
    public string Barrier2Suggestion { get => _barrier2Suggestion; set { _barrier2Suggestion = value; OnPropertyChanged(); } }

    private bool _isCalculating;
    public bool IsCalculating { get => _isCalculating; set { _isCalculating = value; OnPropertyChanged(); } }

    // Propriedades do Martingale
    private bool _isMartingaleEnabled;
    public bool IsMartingaleEnabled 
    { 
        get => _isMartingaleEnabled; 
        set 
        { 
            _isMartingaleEnabled = value; 
            OnPropertyChanged();
            if (value)
            {
                IsNoneSelected = false;
            }
            else
            {
                // Reset martingale state when disabled
                CurrentMartingaleLevel = 0;
                NextStakeAmount = decimal.TryParse(StakeAmount, out decimal currentStake) ? currentStake : 0;
            }
        } 
    }

    private decimal _martingaleFactor = 2.0m;
    public decimal MartingaleFactor 
    { 
        get => _martingaleFactor; 
        set 
        { 
            // Arredonda para duas casas decimais
            _martingaleFactor = Math.Round(value, 2); 
            OnPropertyChanged();
            CalculateNextStake();
        } 
    }

    private int _martingaleLevel = 3;
    public int MartingaleLevel 
    { 
        get => _martingaleLevel; 
        set 
        { 
            _martingaleLevel = value; 
            OnPropertyChanged();
        } 
    }

    private bool _isFastMartingale;
    public bool IsFastMartingale 
    { 
        get => _isFastMartingale; 
        set 
        { 
            _isFastMartingale = value; 
            OnPropertyChanged();
            
            if (value && IsMartingaleEnabled)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population");
                
                // IMMEDIATE AGGRESSIVE POPULATION: Ensure pool is ready instantly
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await PopulateHotProposalPoolImmediate();
                        
                        lock (_poolLock)
                        {
                            var readyLevels = _hotProposalPool.Count;
                            _logger.LogInformation($"[DEBUG] Fast Martingale READY: {readyLevels} proposals pre-calculated and ready for instant execution");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] Fast Martingale enable: Failed to populate hot pool");
                    }
                });
            }
            else if (!value)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale DISABLED - clearing hot pool");
                
                // Clear pool when disabled to save memory
                lock (_poolLock)
                {
                    _hotProposalPool.Clear();
                }
            }
        } 
    }

    // HOT PROPOSAL POOL - Pre-calculated proposals ready for instant execution
    private readonly Dictionary<int, ProposalResponse> _hotProposalPool = new();
    private readonly object _poolLock = new object();
    private bool _isPoolPopulating = false;


    private decimal _nextStakeAmount;
    public decimal NextStakeAmount 
    { 
        get => _nextStakeAmount; 
        set 
        { 
            _nextStakeAmount = value; 
            OnPropertyChanged();
        } 
    }

    private decimal _initialStakeAmount;

    // Propriedade para o radiobutton "Nenhum"
    private bool _isNoneSelected = true;
    public bool IsNoneSelected 
    { 
        get => _isNoneSelected; 
        set 
        { 
            _isNoneSelected = value; 
            OnPropertyChanged();
            if (value)
            {
                IsMartingaleEnabled = false;
            }
        } 
    }
    public decimal InitialStakeAmount 
    { 
        get => _initialStakeAmount; 
        set 
        { 
            _initialStakeAmount = value; 
            OnPropertyChanged();
        } 
    }

    private int _currentMartingaleLevel;
    public int CurrentMartingaleLevel 
    { 
        get => _currentMartingaleLevel; 
        set 
        { 
            _currentMartingaleLevel = value; 
            OnPropertyChanged();
            CalculateNextStake();
        } 
    }

    // Lista completa de símbolos ativos para filtragem
    private List<ActiveSymbol> _allActiveSymbols = new();
    
    public MainViewModel(IDerivApiService derivApiService, ILogger<MainViewModel> logger)
    {
        _derivApiService = derivApiService;
        _logger = logger;
        SubscribeToApiEvents();
        _derivApiService.ConnectAndAuthorizeAsync();
    }

    private void SubscribeToApiEvents()
    {
        _derivApiService.ConnectionEstablished += OnConnectionEstablished;
        _derivApiService.ConnectionLost += OnConnectionLost;
        _derivApiService.AccountInfoUpdated += OnAccountInfoUpdated;
        _derivApiService.PingUpdated += OnPingUpdated;
        _derivApiService.ContractResult += OnContractResultReceived;
        _derivApiService.ContractNearExpiry += OnContractNearExpiry;
    }

    private void OnPingUpdated(long newPing)
    {
        Application.Current.Dispatcher.Invoke(() => Ping = newPing);
    }

    private void OnContractResultReceived(bool isWin)
    {
        var receivedTime = DateTimeOffset.Now;
        
        // IMMEDIATE NON-BLOCKING EXECUTION: No UI thread blocking
        if (IsMartingaleEnabled)
        {
            if (isWin)
            {
                _logger.LogInformation($"[TIMING] Contract WIN at {receivedTime:HH:mm:ss.fff} - calling OnContractWin");
                // Fire-and-forget for win processing
                _ = Task.Run(() => OnContractWin());
            }
            else
            {
                _logger.LogInformation($"[TIMING] Contract LOSS at {receivedTime:HH:mm:ss.fff} - IMMEDIATE EXECUTION");
                // IMMEDIATE EXECUTION: Call directly without UI thread blocking
                OnContractLossImmediate();
            }
        }
        else
        {
            _logger.LogInformation("[DEBUG] Martingale not enabled, ignoring contract result");
        }
    }

    private void OnContractNearExpiry(string contractId)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            _logger.LogInformation($"[DEBUG] OnContractNearExpiry chamado para contrato: {contractId}, IsMartingaleEnabled = {IsMartingaleEnabled}, IsFastMartingale = {IsFastMartingale}");
            
            // Ensure hot pool is ready for instant execution if needed
            if (IsMartingaleEnabled && IsFastMartingale)
            {
                _logger.LogInformation($"[DEBUG] Contrato próximo ao vencimento - garantindo HOT POOL pronto para execução instantânea");
                
                // Trigger immediate pool verification and replenishment if needed
                _ = Task.Run(async () =>
                {
                    lock (_poolLock)
                    {
                        var availableProposals = _hotProposalPool.Count;
                        _logger.LogInformation($"[DEBUG] HOT POOL status: {availableProposals} propostas disponíveis");
                        
                        if (availableProposals < 2)
                        {
                            _logger.LogInformation("[DEBUG] HOT POOL com poucas propostas - iniciando reabastecimento de emergencia");
                        }
                    }
                    
                    // Always ensure pool is fully populated before potential loss
                    await PopulateHotProposalPool();
                });
            }
        });
    }


    private void OnAccountInfoUpdated(string accountCode, string accountType, double balance)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            AccountCode = accountCode;
            // O tipo de conta só vem na autorização, então não atualizamos se for nulo
            if (accountType != null) 
            {
                AccountType = accountType;
            }
            Balance = balance;
        });
    }

    private void OnConnectionLost()
    {
        Application.Current.Dispatcher.Invoke(() => IsConnected = false);
    }

    private void OnConnectionEstablished()
    {
        Application.Current.Dispatcher.Invoke(() => 
        {
            _logger.LogInformation("[DEBUG] ConnectionEstablished evento disparado");
            IsConnected = true;
            // Preserva as seleções atuais para restaurar após reconexão
            var previousMarket = SelectedMarket;
            var previousSubMarket = SelectedSubMarket;
            var previousActiveSymbol = SelectedActiveSymbol?.Symbol;
            var previousContractType = SelectedContractType?.ContractType;
            
            // Carrega os símbolos ativos quando a conexão é estabelecida
            _ = LoadActiveSymbolsAsync().ContinueWith(async _ => 
            {
                // Restaura as seleções após carregar os dados
                await RestoreSelectionsAsync(previousMarket, previousSubMarket, previousActiveSymbol, previousContractType);
                
                // Ativa subscrição para atualizações de saldo em tempo real
                await _derivApiService.SubscribeToBalanceAsync();
            });
        });
    }

    // Métodos para carregar dados da API
    private async Task LoadActiveSymbolsAsync()
    {
        try
        {
            _logger.LogInformation("[DEBUG] LoadActiveSymbolsAsync iniciado");
            var symbols = await _derivApiService.GetActiveSymbolsAsync();
            _logger.LogInformation($"[DEBUG] Recebidos {symbols.Count} símbolos ativos");
            _allActiveSymbols = symbols;
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                // Extrai mercados únicos
                var markets = symbols.Select(s => s.MarketDisplayName).Distinct().OrderBy(m => m).ToList();
                _logger.LogInformation($"[DEBUG] Extraídos {markets.Count} mercados únicos");
                Markets.Clear();
                foreach (var market in markets)
                {
                    Markets.Add(market);
                    _logger.LogInformation($"[DEBUG] Adicionado mercado: {market}");
                }
                _logger.LogInformation($"[DEBUG] Markets.Count após carregamento: {Markets.Count}");
                _logger.LogInformation($"[DEBUG] Markets contém: {string.Join(", ", Markets)}");
                
                // Força notificação de mudança na propriedade Markets
                OnPropertyChanged(nameof(Markets));
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] Erro em LoadActiveSymbolsAsync: {ex.Message}");
            // Log do erro - em uma implementação real, você usaria um logger
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar símbolos ativos: {ex.Message}");
        }
    }

    private async Task RestoreSelectionsAsync(string previousMarket, string previousSubMarket, string previousActiveSymbol, string previousContractType)
    {
        try
        {
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // Restaura o mercado se ainda existir
                if (!string.IsNullOrEmpty(previousMarket) && Markets.Contains(previousMarket))
                {
                    SelectedMarket = previousMarket;
                    
                    // Aguarda submercados carregarem
                    await Task.Yield();
                    
                    // Restaura o submercado se ainda existir
                    if (!string.IsNullOrEmpty(previousSubMarket) && SubMarkets.Contains(previousSubMarket))
                    {
                        SelectedSubMarket = previousSubMarket;
                        
                        // Aguarda símbolos ativos carregarem
                        await Task.Yield();
                        
                        // Restaura o símbolo ativo se ainda existir
                        if (!string.IsNullOrEmpty(previousActiveSymbol))
                        {
                            var symbolToRestore = ActiveSymbols.FirstOrDefault(s => s.Symbol == previousActiveSymbol);
                            if (symbolToRestore != null)
                            {
                                SelectedActiveSymbol = symbolToRestore;
                                
                                // Aguarda tipos de contrato carregarem
                                await Task.Yield();
                                
                                // Restaura o tipo de contrato se ainda existir
                                if (!string.IsNullOrEmpty(previousContractType))
                                {
                                    // Tenta múltiplas vezes se a lista ainda estiver vazia
                                    for (int i = 0; i < 5; i++)
                                    {
                                        var contractToRestore = ContractTypes.FirstOrDefault(c => c.ContractType == previousContractType);
                                        if (contractToRestore != null)
                                        {
                                            SelectedContractType = contractToRestore;
                                            _logger.LogInformation($"[DEBUG] Tipo de contrato restaurado: {previousContractType}");
                                            break;
                                        }
                                        else if (ContractTypes.Count == 0 && i < 4)
                                        {
                                            _logger.LogInformation($"[DEBUG] Lista de contratos ainda vazia, tentativa {i + 1}/5");
                                            await Task.Yield();
                                        }
                                        else
                                        {
                                            _logger.LogWarning($"[DEBUG] Tipo de contrato {previousContractType} não encontrado na lista atual após {i + 1} tentativas");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                _logger.LogInformation($"[DEBUG] Seleções restauradas: Market={SelectedMarket}, SubMarket={SelectedSubMarket}, Symbol={SelectedActiveSymbol?.Symbol}, ContractType={SelectedContractType?.ContractType}");
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] Erro ao restaurar seleções: {ex.Message}");
        }
    }

    // Métodos para lógica de seleção em cascata
    private void OnMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedMarket))
        {
            SubMarkets.Clear();
            ActiveSymbols.Clear();
            ContractTypes.Clear();
            return;
        }

        // Filtra submercados baseado no mercado selecionado
        var subMarkets = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket)
            .Select(s => s.SubmarketDisplayName)
            .Distinct()
            .OrderBy(sm => sm)
            .ToList();

        SubMarkets.Clear();
        foreach (var subMarket in subMarkets)
        {
            SubMarkets.Add(subMarket);
        }

        // Limpa seleções subsequentes
        SelectedSubMarket = null;
        ActiveSymbols.Clear();
        ContractTypes.Clear();
    }

    private void OnSubMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedSubMarket))
        {
            ActiveSymbols.Clear();
            ContractTypes.Clear();
            return;
        }

        // Filtra símbolos ativos baseado no submercado selecionado
        var symbols = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket && s.SubmarketDisplayName == SelectedSubMarket)
            .OrderBy(s => s.DisplayName)
            .ToList();

        ActiveSymbols.Clear();
        foreach (var symbol in symbols)
        {
            ActiveSymbols.Add(symbol);
        }

        // Limpa seleções subsequentes
        SelectedActiveSymbol = null;
        ContractTypes.Clear();
    }

    private async void OnActiveSymbolSelectionChanged()
    {
        if (SelectedActiveSymbol == null)
        {
            ContractTypes.Clear();
            return;
        }

        try
        {
            var contractsResponse = await _derivApiService.GetContractsForSymbolAsync(SelectedActiveSymbol.Symbol);
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                ContractTypes.Clear();
                SelectedContractType = null; // Isso vai chamar o UpdateContractParameters e limpar a UI
                foreach (var contract in contractsResponse.Available.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
                {
                    ContractTypes.Add(contract);
                }
            });
        }
        catch (Exception ex)
        {
            // Log do erro
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar contratos para {SelectedActiveSymbol.Symbol}: {ex.Message}");
        }
    }

    // Lógica para atualizar a UI com base no contrato selecionado
    private void UpdateContractParameters()
    {
        if (SelectedContractType == null)
        {
            IsDurationVisible = false;
            IsBarrier1Visible = false;
            IsBarrier2Visible = false;
            IsDigitSelectionVisible = false;
            DurationInfo = string.Empty;
            Barrier1Suggestion = string.Empty;
            Barrier2Suggestion = string.Empty;
            return;
        }

        // Reset
        IsBarrier1Visible = false;
        IsBarrier2Visible = false;
        IsDigitSelectionVisible = false;
        Barrier1Suggestion = string.Empty;
        Barrier2Suggestion = string.Empty;

        // Sempre mostrar duração
        IsDurationVisible = true;
        DurationInfo = $"Duração: Min {SelectedContractType.MinContractDuration}, Max {SelectedContractType.MaxContractDuration}";

        // Verificar Barreiras e definir sugestões
        if (SelectedContractType.Barriers.HasValue)
        {
            if (SelectedContractType.Barriers >= 1)
            {
                IsBarrier1Visible = true;
                Barrier1Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 1);
            }
            if (SelectedContractType.Barriers == 2)
            {
                IsBarrier2Visible = true;
                Barrier2Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 2);
            }
        }

        // Verificar Dígitos
        if (SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
        {
            IsDigitSelectionVisible = true;
        }

        // Recalcular proposta quando parâmetros mudarem
        CalculateProposalAsync();
    }

    private string GetBarrierSuggestion(string contractType, int barrierNumber)
    {
        return contractType?.ToUpper() switch
        {
            "HIGHER" => "+10.5",
            "LOWER" => "-10.5",
            "TOUCH" => "+15.0",
            "NOTOUCH" => "+20.0",
            "STAYS_IN" when barrierNumber == 1 => "+25.0",
            "STAYS_IN" when barrierNumber == 2 => "-25.0",
            "GOES_OUT" when barrierNumber == 1 => "+30.0",
            "GOES_OUT" when barrierNumber == 2 => "-30.0",
            "ENDS_IN" when barrierNumber == 1 => "+20.0",
            "ENDS_IN" when barrierNumber == 2 => "-20.0",
            "ENDS_OUT" when barrierNumber == 1 => "+35.0",
            "ENDS_OUT" when barrierNumber == 2 => "-35.0",
            _ => "+10.0"
        };
    }

    private async Task CalculateProposalAndBuyAsync()
    {
        var methodStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] CalculateProposalAndBuyAsync iniciado às {methodStartTime:HH:mm:ss.fff}");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null)
        {
            _logger.LogInformation("[TIMING] CalculateProposalAndBuyAsync cancelado - dados insuficientes");
            return;
        }

        try
        {
            var requestPrepTime = DateTimeOffset.Now;
            // Prepare proposal request with minimal overhead
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = decimal.Parse(StakeAmount),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }

            var requestReadyTime = DateTimeOffset.Now;
            var prepDelay = (requestReadyTime - requestPrepTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Request preparado às {requestReadyTime:HH:mm:ss.fff} (prep delay: {prepDelay}ms)");

            // Use SendFastRequestAsync for ultra-low latency when in Fast Martingale mode
            var response = IsFastMartingale ? 
                await _derivApiService.GetFastProposalAsync(request) : 
                await _derivApiService.GetProposalAsync(request);
            
            var proposalReceivedTime = DateTimeOffset.Now;
            var proposalDelay = (proposalReceivedTime - requestReadyTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Proposta recebida às {proposalReceivedTime:HH:mm:ss.fff} (API delay: {proposalDelay}ms)");

            if (response.Error == null && response.Proposal != null)
            {
                var uiUpdateStartTime = DateTimeOffset.Now;
                // Update UI with proposal data (minimal assignments)
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;

                var uiUpdateEndTime = DateTimeOffset.Now;
                var uiUpdateDelay = (uiUpdateEndTime - uiUpdateStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] UI atualizada às {uiUpdateEndTime:HH:mm:ss.fff} (UI delay: {uiUpdateDelay}ms)");

                // Execute buy immediately without additional checks for maximum speed
                if (IsConnected && AskPrice > 0 && !string.IsNullOrEmpty(CurrentProposalId))
                {
                    var buyStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] Iniciando compra às {buyStartTime:HH:mm:ss.fff}. ProposalId: {CurrentProposalId}, Price: {AskPrice}");
                    await ExecuteBuyCommand();
                    
                    var buyEndTime = DateTimeOffset.Now;
                    var buyDelay = (buyEndTime - buyStartTime).TotalMilliseconds;
                    var totalDelay = (buyEndTime - methodStartTime).TotalMilliseconds;
                    _logger.LogInformation($"[TIMING] Compra concluída às {buyEndTime:HH:mm:ss.fff} (buy delay: {buyDelay}ms, total: {totalDelay}ms)");
                }
                else
                {
                    _logger.LogInformation($"[TIMING] Compra cancelada - condições não atendidas. IsConnected: {IsConnected}, AskPrice: {AskPrice}, ProposalId: {CurrentProposalId}");
                }
            }
            else
            {
                _logger.LogInformation($"[TIMING] Proposta inválida recebida. Error: {response.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            var errorTime = DateTimeOffset.Now;
            var errorDelay = (errorTime - methodStartTime).TotalMilliseconds;
            _logger.LogError(ex, $"[TIMING] Erro em CalculateProposalAndBuyAsync às {errorTime:HH:mm:ss.fff} (após {errorDelay}ms)");
        }
    }

    private async void CalculateProposalAsync()
    {
        _logger.LogInformation("[DEBUG] CalculateProposalAsync chamado");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null || IsCalculating)
        {
            _logger.LogInformation($"[DEBUG] Saindo: SelectedContractType={SelectedContractType?.ContractType}, SelectedActiveSymbol={SelectedActiveSymbol?.Symbol}, IsCalculating={IsCalculating}");
            return;
        }

        // Verificar se todos os campos obrigatórios estão preenchidos
        if (IsBarrier1Visible && string.IsNullOrWhiteSpace(Barrier1Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier1 obrigatória mas vazia. IsBarrier1Visible={IsBarrier1Visible}, Barrier1Value='{Barrier1Value}'");
            return;
        }
        if (IsBarrier2Visible && string.IsNullOrWhiteSpace(Barrier2Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier2 obrigatória mas vazia. IsBarrier2Visible={IsBarrier2Visible}, Barrier2Value='{Barrier2Value}'");
            return;
        }
        if (DurationValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationValue inválida: {DurationValue}");
            return;
        }
        if (string.IsNullOrWhiteSpace(DurationUnit))
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationUnit vazia: '{DurationUnit}'");
            return;
        }
        if (!decimal.TryParse(StakeAmount, out decimal stakeValue) || stakeValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: StakeAmount inválido: '{StakeAmount}'");
            return;
        }
        
        _logger.LogInformation($"[DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType={SelectedContractType.ContractType}, Symbol={SelectedActiveSymbol.Symbol}, Stake={stakeValue}");

        try
        {
            IsCalculating = true;

            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = stakeValue,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };
            
            // Só incluir LastDigitPrediction se for necessário para este tipo de contrato
            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            // Não definir LastDigitPrediction quando não necessário - deixar como não definido

            var response = await _derivApiService.GetProposalAsync(request);

            if (response.Error != null)
            {
                _logger.LogWarning($"Erro na proposta: {response.Error.Message}");
                CalculatedPayout = 0;
                AskPrice = 0;
                CalculatedBarrier1 = string.Empty;
                CalculatedBarrier2 = string.Empty;
            }
            else if (response.Proposal != null)
            {
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao calcular proposta");
            CalculatedPayout = 0;
            AskPrice = 0;
            CalculatedBarrier1 = string.Empty;
            CalculatedBarrier2 = string.Empty;
        }
        finally
        {
            IsCalculating = false;
        }
    }

    // Comando para compra
    private ICommand _buyCommand;
    public ICommand BuyCommand
    {
        get
        {
            return _buyCommand ??= new RelayCommand(async () => await ExecuteBuyCommand(), CanExecuteBuy);
        }
    }

    private bool CanExecuteBuy()
    {
        return IsConnected && 
               SelectedActiveSymbol != null && 
               SelectedContractType != null && 
               Stake > 0 && 
               DurationValue > 0 && 
               !string.IsNullOrEmpty(DurationUnit) &&
               AskPrice > 0 &&
               !string.IsNullOrEmpty(CurrentProposalId);
    }

    private async Task ExecuteBuyCommand()
    {
        try
        {
            // Realizar a compra usando a API da Deriv
            var buyResponse = await _derivApiService.BuyContractAsync(CurrentProposalId, AskPrice);

            if (buyResponse.Error != null)
            {
                _logger.LogError("Erro na compra: {ErrorMessage}", buyResponse.Error.Message);
                return;
            }

            if (buyResponse.Buy != null)
            {
                _logger.LogInformation("Compra executada com sucesso. ContractId: {ContractId}, TransactionId: {TransactionId}", 
                                      buyResponse.Buy.ContractId, buyResponse.Buy.TransactionId);

                // CRITICAL: IMMEDIATE SYNCHRONOUS HOT POOL POPULATION
                // Must populate pool NOW before any potential loss occurs
                if (IsMartingaleEnabled && IsFastMartingale)
                {
                    var preCalcStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às {preCalcStartTime:HH:mm:ss.fff}");
                    
                    // SYNCHRONOUS population - block until complete to ensure proposals are ready
                    try
                    {
                        await PopulateHotProposalPoolImmediate();
                        
                        var preCalcEndTime = DateTimeOffset.Now;
                        var preCalcDuration = (preCalcEndTime - preCalcStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em {preCalcDuration}ms - propostas GARANTIDAMENTE prontas");
                        
                        lock (_poolLock)
                        {
                            _logger.LogInformation($"[DEBUG] HOT POOL STATUS: {_hotProposalPool.Count} propostas prontas nos níveis: [{string.Join(", ", _hotProposalPool.Keys)}]");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] IMMEDIATE SYNC HOT POOL: ERRO CRÍTICO no pré-cálculo síncrono");
                    }
                }

                // Recalcular proposta automaticamente para manter o botão pronto
                CalculateProposalAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar compra");
        }
    }

    // Métodos do Martingale
    private void CalculateNextStake()
    {
        if (!IsMartingaleEnabled)
        {
            NextStakeAmount = decimal.TryParse(StakeAmount, out decimal currentStake) ? currentStake : 0;
            return;
        }

        if (InitialStakeAmount == 0 && decimal.TryParse(StakeAmount, out decimal initialStake))
        {
            InitialStakeAmount = initialStake;
        }

        if (CurrentMartingaleLevel == 0)
        {
            NextStakeAmount = InitialStakeAmount;
        }
        else
        {
            NextStakeAmount = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, CurrentMartingaleLevel);
        }
    }

    // IMMEDIATE HOT PROPOSAL POOL - Synchronous population for instant execution
    private async Task PopulateHotProposalPoolImmediate()
    {
        if (_isPoolPopulating || !IsFastMartingale || !IsMartingaleEnabled) return;
        
        _isPoolPopulating = true;
        var poolStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at {poolStartTime:HH:mm:ss.fff}");
        
        try
        {
            // Validate required fields
            if (SelectedContractType?.ContractType == null || SelectedActiveSymbol?.Symbol == null)
            {
                _logger.LogWarning("[DEBUG] HOT POOL IMMEDIATE: Missing required fields - canceling population");
                return;
            }
            
            // AGGRESSIVE POPULATION: Clear and rebuild entire pool for freshness
            lock (_poolLock)
            {
                _hotProposalPool.Clear();
                _logger.LogInformation("[DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild");
            }
            
            // PRE-EMPTIVE POPULATION: Calculate for ALL possible next levels (1-5)
            var populationTasks = new List<Task>();
            
            for (int level = 1; level <= 5; level++)
            {
                var levelTask = Task.Run(async () =>
                {
                    try
                    {
                        var levelStartTime = DateTimeOffset.Now;
                        var futureStake = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level);
                        
                        var request = new ProposalRequest
                        {
                            ContractType = SelectedContractType.ContractType,
                            Symbol = SelectedActiveSymbol.Symbol,
                            Duration = DurationValue,
                            DurationUnit = DurationUnit,
                            Currency = "USD",
                            Stake = futureStake,
                            Barrier = IsBarrier1Visible ? Barrier1Value : null,
                            Barrier2 = IsBarrier2Visible ? Barrier2Value : null
                        };

                        if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
                        {
                            request.LastDigitPrediction = SelectedDigit;
                        }
                        
                        // FAST PROPOSAL REQUEST: Use high-speed API
                        var response = await _derivApiService.GetFastProposalAsync(request);
                        
                        if (response?.Error == null && response?.Proposal != null)
                        {
                            lock (_poolLock)
                            {
                                _hotProposalPool[level] = response;
                            }
                            
                            var levelEndTime = DateTimeOffset.Now;
                            var levelDelay = (levelEndTime - levelStartTime).TotalMilliseconds;
                            _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Level {level} populated in {levelDelay}ms. Stake: {futureStake:F2}, ProposalId: {response.Proposal.Id}");
                        }
                        else
                        {
                            _logger.LogError($"[DEBUG] HOT POOL AGGRESSIVE: Error populating level {level}: {response?.Error?.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[DEBUG] HOT POOL AGGRESSIVE: Exception in level {level}");
                    }
                });
                
                populationTasks.Add(levelTask);
            }
            
            // PARALLEL EXECUTION: Wait for all levels to complete
            await Task.WhenAll(populationTasks);
            
            var poolEndTime = DateTimeOffset.Now;
            var totalPoolTime = (poolEndTime - poolStartTime).TotalMilliseconds;
            
            lock (_poolLock)
            {
                _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Population completed in {totalPoolTime}ms. Pool contains {_hotProposalPool.Count} proposals GUARANTEED ready. Levels: [{string.Join(", ", _hotProposalPool.Keys)}]");
                
                // VALIDATION: Ensure we have at least the next 2 levels ready
                var nextLevel = CurrentMartingaleLevel + 1;
                var hasNextLevel = _hotProposalPool.ContainsKey(nextLevel);
                var hasSecondLevel = _hotProposalPool.ContainsKey(nextLevel + 1);
                
                if (!hasNextLevel || !hasSecondLevel)
                {
                    _logger.LogWarning($"[DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: {hasNextLevel}, Second: {hasSecondLevel}. Current: {CurrentMartingaleLevel}");
                }
                else
                {
                    _logger.LogInformation($"[DEBUG] HOT POOL VALIDATION: Critical levels ready. Pool is OPTIMAL for instant execution.");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DEBUG] HOT POOL IMMEDIATE: Critical error in aggressive population");
        }
        finally
        {
            _isPoolPopulating = false;
        }
    }

    // LEGACY: Async hot proposal pool for backward compatibility
    private async Task PopulateHotProposalPool()
    {
        // Redirect to immediate synchronous version for consistency
        await PopulateHotProposalPoolImmediate();
    }
    
    // Replenish used proposal in the pool (optimized for background execution)
    private async Task ReplenishHotProposal(int level)
    {
        try
        {
            if (!IsFastMartingale || !IsMartingaleEnabled) return;
            
            var replenishStartTime = DateTimeOffset.Now;
            var futureStake = InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level);
            
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = futureStake,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            
            var response = await _derivApiService.GetFastProposalAsync(request);
            
            if (response?.Error == null && response?.Proposal != null)
            {
                lock (_poolLock)
                {
                    _hotProposalPool[level] = response;
                }
                
                var replenishEndTime = DateTimeOffset.Now;
                var replenishDelay = (replenishEndTime - replenishStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] HOT POOL REPLENISH: Nível {level} reabastecido em {replenishDelay}ms. ProposalId: {response.Proposal.Id}");
            }
            else
            {
                _logger.LogError($"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}: {response?.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}");
        }
    }

    // IMMEDIATE LOSS EXECUTION: Zero-delay processing for fast martingale
    private void OnContractLossImmediate()
    {
        var startTime = DateTimeOffset.Now;
        
        if (!IsMartingaleEnabled || CurrentMartingaleLevel >= MartingaleLevel) 
        {
            if (CurrentMartingaleLevel >= MartingaleLevel)
            {
                _logger.LogInformation($"[DEBUG] Max martingale level reached ({MartingaleLevel}), resetting");
                ResetMartingale();
            }
            return;
        }

        // INSTANT LEVEL INCREMENT
        CurrentMartingaleLevel++;
        StakeAmount = NextStakeAmount.ToString("F2");
        
        if (!IsFastMartingale)
        {
            _logger.LogInformation("[DEBUG] Fast Martingale disabled, manual execution required");
            return;
        }

        // IMMEDIATE HOT PROPOSAL RETRIEVAL
        ProposalResponse hotProposal = null;
        lock (_poolLock)
        {
            if (_hotProposalPool.TryGetValue(CurrentMartingaleLevel, out hotProposal))
            {
                _hotProposalPool.Remove(CurrentMartingaleLevel);
            }
        }
        
        var retrievalTime = DateTimeOffset.Now;
        var retrievalDelay = (retrievalTime - startTime).TotalMilliseconds;
        
        if (hotProposal?.Error == null && hotProposal?.Proposal != null)
        {
            _logger.LogInformation($"[TIMING] INSTANT EXECUTION: Hot proposal retrieved in {retrievalDelay}ms. Level: {CurrentMartingaleLevel}, Stake: {StakeAmount}, ProposalId: {hotProposal.Proposal.Id}");
            
            // DIRECT WEBSOCKET SEND: Zero additional processing
            _derivApiService.BuyContractImmediateAsync(hotProposal.Proposal.Id, hotProposal.Proposal.AskPrice, null);
            
            var executionTime = DateTimeOffset.Now;
            var totalTime = (executionTime - startTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] INSTANT DISPATCH: Buy order sent in {totalTime}ms total");
            
            // MINIMAL UI UPDATE: Non-blocking background update
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                CurrentProposalId = hotProposal.Proposal.Id;
                AskPrice = hotProposal.Proposal.AskPrice;
                CalculatedPayout = hotProposal.Proposal.Payout;
            }), System.Windows.Threading.DispatcherPriority.Background);
            
            // BACKGROUND REPLENISHMENT: Fire-and-forget
            _ = Task.Run(() => ReplenishHotProposal(CurrentMartingaleLevel));
        }
        else
        {
            var emergencyTime = DateTimeOffset.Now;
            var emergencyDelay = (emergencyTime - startTime).TotalMilliseconds;
            _logger.LogError($"[TIMING] EMERGENCY FALLBACK: No hot proposal available in {emergencyDelay}ms. Level: {CurrentMartingaleLevel}");
            
            // EMERGENCY: Use instant market buy as absolute fallback
            var emergencyRequest = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = decimal.Parse(StakeAmount),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                emergencyRequest.LastDigitPrediction = SelectedDigit;
            }
            
            _derivApiService.BuyInstantMarketAsync(emergencyRequest, null);
            
            var emergencyExecutionTime = DateTimeOffset.Now;
            var emergencyTotalTime = (emergencyExecutionTime - startTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] EMERGENCY EXECUTION: Fallback sent in {emergencyTotalTime}ms");
            
            // EMERGENCY POOL REBUILD: Background
            _ = Task.Run(() => PopulateHotProposalPoolImmediate());
        }
    }

    // LEGACY: Keep for backward compatibility
    public void OnContractLoss()
    {
        OnContractLossImmediate();
    }

    public void OnContractWin()
    {
        if (!IsMartingaleEnabled) return;
        
        // Reset to initial stake after a win
        ResetMartingale();
    }

    private void ResetMartingale()
    {
        CurrentMartingaleLevel = 0;
        StakeAmount = InitialStakeAmount.ToString("F2");
        CalculateNextStake();
    }

    // Override do StakeAmount para integrar com Martingale
    private string _stakeAmount = "1.00";
    public string StakeAmount
    {
        get => _stakeAmount;
        set
        {
            _stakeAmount = value;
            OnPropertyChanged();
            
            // Sincronizar com a propriedade Stake para CanExecuteBuy funcionar
            if (decimal.TryParse(value, out decimal stakeValue))
            {
                _stake = stakeValue;
                OnPropertyChanged(nameof(Stake));
            }
            
            // Update initial stake if martingale is enabled and this is a manual change
            if (IsMartingaleEnabled && decimal.TryParse(value, out decimal stake))
            {
                if (CurrentMartingaleLevel == 0)
                {
                    InitialStakeAmount = stake;
                }
            }
            
            CalculateNextStake();
            CalculateProposalAsync();
        }
    }
}