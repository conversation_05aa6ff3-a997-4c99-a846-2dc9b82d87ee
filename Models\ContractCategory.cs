using System.Text.Json.Serialization;

namespace Excalibur.Models;

public class ContractCategory
{
    [JsonPropertyName("contract_category_display")]
    public string CategoryDisplay { get; set; } = string.Empty;

    [JsonPropertyName("contract_display")]
    public string ContractDisplay { get; set; } = string.Empty;

    [JsonPropertyName("contract_type")]
    public string ContractType { get; set; } = string.Empty;
}