using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.WebSockets;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Websocket.Client;
using Excalibur.Models;

namespace Excalibur.Services;

public class DerivApiService : IDerivApiService
{
    private readonly ILogger<DerivApiService> _logger;
    private IWebsocketClient _ws;
    private Timer _pingTimer;
    private readonly Stopwatch _pingStopwatch = new();
    private DateTime _lastPongReceived = DateTime.UtcNow;
    private Timer _connectionMonitorTimer;

    // Dicionário para rastrear requisições e suas respostas
    private readonly ConcurrentDictionary<int, TaskCompletionSource<JsonElement>> _pendingRequests = new();
    private int _nextRequestId = 1;
    private readonly HashSet<string> _processedContracts = new HashSet<string>();

    // Configurações - virão da UI no futuro
    private string _apiToken = "oLJLFtINRDBGUh1";
    private int _appId = 82663;

    public bool IsConnected => _ws?.IsRunning ?? false;

    public event Action<string, string, double> AccountInfoUpdated;
    public event Action<long> PingUpdated;
    public event Action ConnectionEstablished;
    public event Action ConnectionLost;
    public event Action<bool> ContractResult; // true = win, false = loss
    public event Action<string> ContractNearExpiry; // contract_id when contract is near expiry

    public DerivApiService(ILogger<DerivApiService> logger)
    {
        _logger = logger;
        InitializeClient();
    }

    private void InitializeClient()
    {
        var url = new Uri($"wss://ws.binaryws.com/websockets/v3?app_id={_appId}");
        _ws = new WebsocketClient(url);

        _ws.ReconnectTimeout = TimeSpan.FromSeconds(30); // Tempo para tentar reconectar
        _ws.ErrorReconnectTimeout = TimeSpan.FromSeconds(30);

        _ws.ReconnectionHappened.Subscribe(info => {
            _logger.LogInformation($"Reconexão bem-sucedida: {info.Type}");
            Authorize();
        });

        _ws.DisconnectionHappened.Subscribe(info => {
            _logger.LogWarning($"Conexão perdida: {info.Type}");
            // Clean up both timers on disconnection
            _pingTimer?.Dispose();
            _connectionMonitorTimer?.Dispose();
            ConnectionLost?.Invoke();
        });

        _ws.MessageReceived.Subscribe(msg => {
            // Removed verbose message logging for performance optimization
            ProcessGeneralMessage(msg.Text);
        });
    }

    public async Task ConnectAndAuthorizeAsync()
    {
        _logger.LogInformation("Conectando à API Deriv...");
        await _ws.Start();
        if (_ws.IsRunning)
        {
            Authorize();
        }
    }

    private void Authorize()
    {
        var authRequest = new { authorize = _apiToken };
        _ws.Send(JsonSerializer.Serialize(authRequest));
        // Reduced logging for performance - only log on success/failure
    }
    
    public async Task SubscribeToBalanceAsync()
    {
        var request = new { balance = 1, subscribe = 1 };
        await SendRequestAsync(request);
        // Reduced logging for performance
    }
    
    private void StartPingTimer()
    {
        // Ultra-optimized heartbeat: ping every 1 second for maximum responsiveness
        _pingTimer = new Timer(_ => SendPing(), null, 0, 1000);
        
        // Start connection monitor to detect timeouts more frequently
        _connectionMonitorTimer = new Timer(_ => CheckConnectionHealth(), null, 3000, 3000);
        _lastPongReceived = DateTime.UtcNow;
    }

    private void SendPing()
    {
        try
        {
            if (_ws?.IsRunning == true)
            {
                _pingStopwatch.Restart();
                var pingRequest = new { ping = 1 };
                _ws.Send(JsonSerializer.Serialize(pingRequest));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao enviar ping - conexão pode estar instável");
        }
    }
    
    private void CheckConnectionHealth()
    {
        try
        {
            var timeSinceLastPong = DateTime.UtcNow - _lastPongReceived;
            
            // If no pong received in 4 seconds, consider connection unhealthy
            if (timeSinceLastPong.TotalSeconds > 4 && _ws?.IsRunning == true)
            {
                _logger.LogWarning($"Conexão não responsiva há {timeSinceLastPong.TotalSeconds:F1}s - forçando reconexão");
                
                // Stop timers before reconnection
                _pingTimer?.Dispose();
                _connectionMonitorTimer?.Dispose();
                
                // Trigger reconnection
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _ws.Stop(WebSocketCloseStatus.NormalClosure, "Connection health check failed");
                        await Task.Delay(1000); // Brief delay before reconnecting
                        await _ws.Start();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erro durante reconexão forçada");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no monitoramento de saúde da conexão");
        }
    }

    // Novo método para processar QUALQUER mensagem da API
    private void ProcessGeneralMessage(string jsonMessage)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonMessage);
            var root = doc.RootElement;

            // Fast-track contract-related messages for martingale speed
            if (root.TryGetProperty("msg_type", out var msgTypeElement))
            {
                var msgType = msgTypeElement.GetString();
                
                // Priority processing for time-critical messages
                if (msgType == "proposal_open_contract" || msgType == "portfolio")
                {
                    ProcessMessage(jsonMessage);
                    return;
                }
            }

            // Verifica se a mensagem é uma resposta a uma de nossas requisições
            if (root.TryGetProperty("req_id", out var reqIdElement) && 
                _pendingRequests.TryRemove(reqIdElement.GetInt32(), out var tcs))
            {
                // Se for, completa a Task com o resultado
                if (root.TryGetProperty("error", out var error))
                {
                    tcs.SetException(new Exception(error.GetProperty("message").GetString()));
                }
                else
                {
                    tcs.SetResult(root.Clone());
                }
                return;
            }

            // Processa mensagens que não são respostas diretas (streams, etc.)
            ProcessMessage(jsonMessage); // Chamamos o método antigo para compatibilidade
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem geral da API.");
        }
    }

    private void ProcessMessage(string jsonMessage)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonMessage);
            var root = doc.RootElement;
            var msgType = root.GetProperty("msg_type").GetString();

            switch (msgType)
            {
                case "authorize":
                    if (root.TryGetProperty("error", out var error))
                    {
                        _logger.LogError($"Erro de autorização: {error.GetProperty("message").GetString()}");
                        return;
                    }

                    var authResponse = root.GetProperty("authorize");
                    var loginid = authResponse.GetProperty("loginid").GetString();
                    var accountType = authResponse.GetProperty("is_virtual").GetInt32() == 1 ? "Virtual" : "Real";
                    var balance = authResponse.GetProperty("balance").GetDouble();
                    AccountInfoUpdated?.Invoke(loginid, accountType, balance);
                    
                    // Inicia o timer de ping após autorização bem-sucedida
                    StartPingTimer();
                    
                    // Dispara o evento de conexão estabelecida após autorização bem-sucedida
                    ConnectionEstablished?.Invoke();
                    
                    // Subscrever para atualizações de saldo e portfolio
                    _ = Task.Run(async () => {
                        await SubscribeToBalanceAsync();
                        await SubscribeToPortfolioAsync();
                    });
                    break;
                
                case "balance":
                    var balanceResponse = root.GetProperty("balance");
                    var updatedBalance = balanceResponse.GetProperty("balance").GetDouble();
                    var loginIdBalance = balanceResponse.GetProperty("loginid").GetString();
                    // Assumindo que o tipo de conta não muda
                    AccountInfoUpdated?.Invoke(loginIdBalance, null, updatedBalance);
                    break;
                    
                case "ping":
                    _pingStopwatch.Stop();
                    _lastPongReceived = DateTime.UtcNow; // Update last pong timestamp for connection health monitoring
                    PingUpdated?.Invoke(_pingStopwatch.ElapsedMilliseconds);
                    break;
                    
                case "portfolio":
                    ProcessPortfolioUpdate(root);
                    break;
                    
                case "proposal_open_contract":
                    ProcessOpenContractUpdate(root);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem da API.");
        }
    }

    // Método genérico para enviar uma requisição e aguardar a resposta
    private async Task<JsonElement> SendRequestAsync<T>(T request)
    {
        var requestId = Interlocked.Increment(ref _nextRequestId);
        var tcs = new TaskCompletionSource<JsonElement>();
        _pendingRequests[requestId] = tcs;

        // Adiciona o req_id ao objeto de requisição
        var jsonRequest = JsonDocument.Parse(JsonSerializer.Serialize(request)).RootElement;
        var finalRequest = new Dictionary<string, object>();
        foreach (var property in jsonRequest.EnumerateObject())
        {
            finalRequest[property.Name] = property.Value;
        }
        finalRequest["req_id"] = requestId;

        _ws.Send(JsonSerializer.Serialize(finalRequest));

        // Timeout otimizado: reduzido para 2 segundos
        var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(2000));
        if (completedTask != tcs.Task)
        {
            _pendingRequests.TryRemove(requestId, out _);
            throw new TimeoutException("A requisição para a API Deriv expirou.");
        }
        
        return await tcs.Task;
    }

    // Método otimizado para requisições críticas do Fast Martingale
    private async Task<JsonElement> SendFastRequestAsync<T>(T request)
    {
        var requestId = Interlocked.Increment(ref _nextRequestId);
        var tcs = new TaskCompletionSource<JsonElement>();
        _pendingRequests[requestId] = tcs;

        // Adiciona o req_id ao objeto de requisição
        var jsonRequest = JsonDocument.Parse(JsonSerializer.Serialize(request)).RootElement;
        var finalRequest = new Dictionary<string, object>();
        foreach (var property in jsonRequest.EnumerateObject())
        {
            finalRequest[property.Name] = property.Value;
        }
        finalRequest["req_id"] = requestId;

        _ws.Send(JsonSerializer.Serialize(finalRequest));

        // Timeout ultra-baixo para Fast Martingale: reduzido para 800ms
        var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(800));
        if (completedTask != tcs.Task)
        {
            _pendingRequests.TryRemove(requestId, out _);
            throw new TimeoutException("Requisição Fast Martingale expirou - latência alta detectada.");
        }
        
        return await tcs.Task;
    }

    // Implementação dos novos métodos da interface
    public async Task<List<ActiveSymbol>> GetActiveSymbolsAsync()
    {
        var request = new { active_symbols = "full", product_type = "basic" };
        var response = await SendRequestAsync(request);
        var symbols = response.GetProperty("active_symbols").Deserialize<List<ActiveSymbol>>();
        return symbols ?? new List<ActiveSymbol>();
    }

    public async Task<ContractsForSymbol> GetContractsForSymbolAsync(string symbol)
    {
        var request = new { contracts_for = symbol };
        var response = await SendRequestAsync(request);
        
        // A propriedade raiz é "contracts_for"
        var contractsData = response.GetProperty("contracts_for").Deserialize<ContractsForSymbol>();
        return contractsData ?? new ContractsForSymbol();
    }

    public async Task<ProposalResponse> GetProposalAsync(ProposalRequest request)
    {
        var proposalRequest = new Dictionary<string, object>
        {
            ["proposal"] = 1,
            ["contract_type"] = request.ContractType,
            ["symbol"] = request.Symbol,
            ["duration"] = request.Duration,
            ["duration_unit"] = request.DurationUnit,
            ["currency"] = request.Currency,
            ["basis"] = request.Basis,
            ["amount"] = request.Stake
        };

        // Adicionar campos opcionais apenas se não forem nulos
        if (!string.IsNullOrEmpty(request.Barrier))
            proposalRequest["barrier"] = request.Barrier;
            
        if (!string.IsNullOrEmpty(request.Barrier2))
            proposalRequest["barrier2"] = request.Barrier2;
            
        if (request.LastDigitPrediction.HasValue)
            proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

        var response = await SendFastRequestAsync(proposalRequest);
        var proposalResponse = response.Deserialize<ProposalResponse>();
        return proposalResponse ?? new ProposalResponse();
    }

    public async Task<ProposalResponse> GetFastProposalAsync(ProposalRequest request)
    {
        var proposalRequest = new Dictionary<string, object>
        {
            ["proposal"] = 1,
            ["contract_type"] = request.ContractType,
            ["symbol"] = request.Symbol,
            ["duration"] = request.Duration,
            ["duration_unit"] = request.DurationUnit,
            ["currency"] = request.Currency,
            ["basis"] = request.Basis,
            ["amount"] = request.Stake
        };

        // Adicionar campos opcionais apenas se não forem nulos
        if (!string.IsNullOrEmpty(request.Barrier))
            proposalRequest["barrier"] = request.Barrier;
            
        if (!string.IsNullOrEmpty(request.Barrier2))
            proposalRequest["barrier2"] = request.Barrier2;
            
        if (request.LastDigitPrediction.HasValue)
            proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

        // Use the fastest possible request method with minimal timeout
        var response = await SendFastRequestAsync(proposalRequest);
        var proposalResponse = response.Deserialize<ProposalResponse>();
        return proposalResponse ?? new ProposalResponse();
    }

    public async Task<BuyResponse> BuyContractAsync(string proposalId, decimal price)
    {
        var request = new
        {
            buy = proposalId,
            price = price,
            subscribe = 1  // Subscribe to contract updates
        };

        var response = await SendFastRequestAsync(request);
        var buyResponse = response.Deserialize<BuyResponse>();
        
        // Log contract purchase for debugging
        if (buyResponse?.Buy != null)
        {
            _logger.LogInformation($"[DEBUG] Contrato comprado: {buyResponse.Buy.ContractId}, subscrevendo para atualizações");
        }
        
        return buyResponse;
    }
    
    // Ultra-fast same-time buy method for Fast Martingale - fire and forget approach
    public void BuyContractImmediateAsync(string proposalId, decimal price, Action<BuyResponse> onComplete = null)
    {
        var immediateStartTime = DateTimeOffset.Now;
        
        // Create buy request
        var request = new
        {
            buy = proposalId,
            price = price,
            subscribe = 1
        };

        // Send immediately without waiting for response - fire and forget for same-time execution
        var jsonRequest = JsonSerializer.Serialize(request);
        
        try
        {
            // Send the WebSocket message immediately
            _ws.Send(jsonRequest);
            
            var sendTime = DateTimeOffset.Now;
            var sendDelay = (sendTime - immediateStartTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] SAME-TIME BUY: Mensagem de compra enviada em {sendDelay}ms às {sendTime:HH:mm:ss.fff}");
            
            // Handle response asynchronously without blocking
            if (onComplete != null)
            {
                // Set up a temporary response handler (simplified for immediate execution)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Wait briefly for response (non-blocking)
                        await Task.Delay(100); // Give time for server response
                        
                        // For immediate execution, we don't wait for the actual response
                        // The success will be confirmed by contract updates
                        var mockResponse = new BuyResponse(); // Simplified response
                        onComplete(mockResponse);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erro no callback de compra imediata");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar compra imediata");
        }
    }

    public async Task SubscribeToPortfolioAsync()
    {
        var request = new { portfolio = 1 };
        _ws.Send(JsonSerializer.Serialize(request));
        // Reduced logging for performance
    }

    private void ProcessPortfolioUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("portfolio", out var portfolio) && 
                portfolio.TryGetProperty("contracts", out var contracts))
            {
                foreach (var contract in contracts.EnumerateArray())
                {
                    var contractId = contract.TryGetProperty("contract_id", out var id) ? 
                        (id.ValueKind == JsonValueKind.String ? id.GetString() : id.GetInt64().ToString()) : "unknown";
                    var isSold = contract.TryGetProperty("is_sold", out var sold) ? sold.GetInt32() : 0;
                    
                    if (isSold == 1 && !string.IsNullOrEmpty(contractId) && !_processedContracts.Contains(contractId))
                    {
                        // Contract is settled, check if it's a win or loss
                        if (contract.TryGetProperty("profit", out var profit))
                        {
                            var profitValue = profit.GetDecimal();
                            bool isWin = profitValue > 0;
                            
                            // Only log contract results for debugging critical events
                            _logger.LogInformation($"Contract {contractId} settled: Profit={profitValue}, Win={isWin}");
                            
                            // Marcar contrato como processado
                            _processedContracts.Add(contractId);
                            
                            ContractResult?.Invoke(isWin);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar atualização do portfolio.");
        }
    }
    
    private void ProcessOpenContractUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("proposal_open_contract", out var contract))
            {
                if (contract.TryGetProperty("contract_id", out var contractIdElement))
                {
                    string contractId;
                    
                    // Handle both string and number types for contract_id
                    if (contractIdElement.ValueKind == JsonValueKind.String)
                    {
                        contractId = contractIdElement.GetString();
                    }
                    else if (contractIdElement.ValueKind == JsonValueKind.Number)
                    {
                        contractId = contractIdElement.GetInt64().ToString();
                    }
                    else
                    {
                        return; // Skip if neither string nor number
                    }
                    
                    // Check if contract is near expiry (for Fast Martingale pre-calculation)
                    if (contract.TryGetProperty("date_expiry", out var dateExpiryElement) && 
                        contract.TryGetProperty("is_sold", out var isSoldElement))
                    {
                        var isSold = isSoldElement.GetInt32();
                        
                        if (isSold == 0) // Contract is still active
                        {
                            var dateExpiry = dateExpiryElement.GetInt64();
                            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            var timeToExpiry = dateExpiry - currentTime;
                            
                            // If contract expires in 3 seconds or less, trigger near expiry event
                            // Increased back to 3 seconds for adequate pre-calculation time
                            if (timeToExpiry <= 3 && timeToExpiry > 0)
                            {
                                _logger.LogInformation($"Contract {contractId} near expiry: {timeToExpiry}s");
                                ContractNearExpiry?.Invoke(contractId);
                            }
                            
                            // IMMEDIATE DETECTION: Check if contract has just expired (within 1 second)
                            if (timeToExpiry <= 0 && timeToExpiry >= -1 && !_processedContracts.Contains(contractId))
                            {
                                if (contract.TryGetProperty("profit", out var profitElement))
                                {
                                    var profit = profitElement.GetDecimal();
                                    bool isWin = profit > 0;
                                    
                                    var immediateResultTime = DateTimeOffset.Now;
                                    _logger.LogInformation($"[TIMING] IMMEDIATE - Contrato {contractId} expirado às {immediateResultTime:HH:mm:ss.fff} - Profit: {profit}, Win: {isWin}");
                                    
                                    // Mark as processed immediately to avoid duplicate processing
                                    _processedContracts.Add(contractId);
                                    
                                    // Trigger result immediately on expiry
                                    ContractResult?.Invoke(isWin);
                                    
                                    var immediateEventEnd = DateTimeOffset.Now;
                                    var immediateEventDuration = (immediateEventEnd - immediateResultTime).TotalMilliseconds;
                                    _logger.LogInformation($"[TIMING] IMMEDIATE ContractResult disparado em {immediateEventDuration}ms");
                                    return; // Exit early to avoid double processing
                                }
                            }
                        }
                    }
                    
                    // Fallback: Check if contract is sold/finished (existing logic)
                    if (contract.TryGetProperty("is_sold", out var isSoldElement2))
                    {
                        var isSold = isSoldElement2.GetInt32();
                        
                        if (isSold == 1 && !_processedContracts.Contains(contractId)) // Contract is sold/finished
                        {
                            var contractFinishedTime = DateTimeOffset.Now;
                            _logger.LogInformation($"[TIMING] FALLBACK - Contrato {contractId} detectado como finalizado às {contractFinishedTime:HH:mm:ss.fff}");
                            
                            if (contract.TryGetProperty("profit", out var profitElement))
                            {
                                var profit = profitElement.GetDecimal();
                                bool isWin = profit > 0;
                                _logger.LogInformation($"Contract {contractId} finished: Profit={profit}, Win={isWin}");
                                
                                // Marcar contrato como processado
                                _processedContracts.Add(contractId);
                                
                                var beforeEventTime = DateTimeOffset.Now;
                                _logger.LogInformation($"[TIMING] Disparando ContractResult event às {beforeEventTime:HH:mm:ss.fff}");
                                
                                // Trigger contract result event
                                ContractResult?.Invoke(isWin);
                                
                                var afterEventTime = DateTimeOffset.Now;
                                var eventDuration = (afterEventTime - beforeEventTime).TotalMilliseconds;
                                _logger.LogInformation($"[TIMING] ContractResult event concluído às {afterEventTime:HH:mm:ss.fff} (duração: {eventDuration}ms)");
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar atualização de contrato aberto.");
        }
    }
    
    public void GetFastProposalAndBuyAsync(ProposalRequest request, Action<ProposalResponse, BuyResponse> onComplete)
    {
        var operationStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] ZERO-DELAY OPERATION: GetFastProposalAndBuyAsync iniciado às {operationStartTime:HH:mm:ss.fff}");
        
        // Execute both operations in parallel for maximum speed
        _ = Task.Run(async () =>
        {
            try
            {
                var parallelStartTime = DateTimeOffset.Now;
                
                // Step 1: Get proposal with ultra-low latency
                var proposalTask = GetFastProposalAsync(request);
                var proposalResponse = await proposalTask;
                
                var proposalEndTime = DateTimeOffset.Now;
                var proposalDelay = (proposalEndTime - parallelStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY: Proposta recebida em {proposalDelay}ms às {proposalEndTime:HH:mm:ss.fff}");
                
                BuyResponse buyResponse = null;
                
                // Step 2: Execute buy immediately if proposal is valid
                if (proposalResponse?.Error == null && proposalResponse?.Proposal != null)
                {
                    var buyStartTime = DateTimeOffset.Now;
                    
                    // Execute buy with immediate fire-and-forget pattern
                    var buyExecuted = false;
                    var buyTaskCompletion = new TaskCompletionSource<BuyResponse>();
                    
                    BuyContractImmediateAsync(proposalResponse.Proposal.Id, proposalResponse.Proposal.AskPrice, (response) =>
                    {
                        buyExecuted = true;
                        buyResponse = response;
                        buyTaskCompletion.SetResult(response);
                        
                        var buyEndTime = DateTimeOffset.Now;
                        var buyDelay = (buyEndTime - buyStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] ZERO-DELAY: Compra executada em {buyDelay}ms às {buyEndTime:HH:mm:ss.fff}");
                    });
                    
                    // Wait for buy to complete with timeout for safety
                    try
                    {
                        buyResponse = await Task.WhenAny(buyTaskCompletion.Task, Task.Delay(1000)) == buyTaskCompletion.Task 
                            ? await buyTaskCompletion.Task 
                            : new BuyResponse { Error = new ApiError { Message = "Buy timeout" } };
                    }
                    catch (Exception buyEx)
                    {
                        _logger.LogError(buyEx, "[TIMING] ZERO-DELAY: Erro na execução da compra");
                        buyResponse = new BuyResponse { Error = new ApiError { Message = buyEx.Message } };
                    }
                }
                else
                {
                    _logger.LogError($"[TIMING] ZERO-DELAY: Proposta inválida - {proposalResponse?.Error?.Message}");
                    buyResponse = new BuyResponse { Error = new ApiError { Message = "Invalid proposal" } };
                }
                
                var operationEndTime = DateTimeOffset.Now;
                var totalOperationTime = (operationEndTime - operationStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY OPERATION: Total time {totalOperationTime}ms às {operationEndTime:HH:mm:ss.fff}");
                
                // Execute callback with both responses
                onComplete?.Invoke(proposalResponse, buyResponse);
            }
            catch (Exception ex)
            {
                var errorTime = DateTimeOffset.Now;
                var errorDelay = (errorTime - operationStartTime).TotalMilliseconds;
                _logger.LogError(ex, $"[TIMING] ZERO-DELAY OPERATION: Erro crítico após {errorDelay}ms");
                
                // Return error responses
                var errorProposal = new ProposalResponse { Error = new ProposalError { Message = ex.Message } };
                var errorBuy = new BuyResponse { Error = new ApiError { Message = ex.Message } };
                onComplete?.Invoke(errorProposal, errorBuy);
            }
        });
    }

    // REVOLUTIONARY INSTANT BUY: Uses pre-calculated proposal pool for zero-delay execution
    public void BuyInstantMarketAsync(ProposalRequest request, Action<BuyResponse> onComplete = null)
    {
        var instantStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] INSTANT EMERGENCY BUY: Execução iniciada às {instantStartTime:HH:mm:ss.fff}");

        // IMMEDIATE FIRE-AND-FORGET: Create proposal and buy in parallel for absolute minimum delay
        _ = Task.Run(async () =>
        {
            try
            {
                // ULTRA-FAST PARALLEL EXECUTION: Get proposal and prepare buy simultaneously
                var proposalTask = GetFastProposalAsync(request);
                var proposalResponse = await proposalTask;

                if (proposalResponse?.Error == null && proposalResponse?.Proposal != null)
                {
                    var proposalTime = DateTimeOffset.Now;
                    var proposalDelay = (proposalTime - instantStartTime).TotalMilliseconds;
                    _logger.LogInformation($"[TIMING] EMERGENCY PROPOSAL: Obtida em {proposalDelay}ms às {proposalTime:HH:mm:ss.fff}");

                    // IMMEDIATE WEBSOCKET SEND: Zero processing delay
                    var buyRequest = new
                    {
                        buy = proposalResponse.Proposal.Id,
                        price = proposalResponse.Proposal.AskPrice,
                        subscribe = 1
                    };

                    var jsonRequest = JsonSerializer.Serialize(buyRequest);
                    _ws.Send(jsonRequest);

                    var buyTime = DateTimeOffset.Now;
                    var buyDelay = (buyTime - proposalTime).TotalMilliseconds;
                    var totalTime = (buyTime - instantStartTime).TotalMilliseconds;

                    _logger.LogInformation($"[TIMING] EMERGENCY BUY: Enviada em {buyDelay}ms às {buyTime:HH:mm:ss.fff}");
                    _logger.LogInformation($"[TIMING] EMERGENCY TOTAL: {totalTime}ms - EMERGENCY execution completed");

                    // Success callback
                    if (onComplete != null)
                    {
                        var successResponse = new BuyResponse
                        {
                            Buy = new BuyContract
                            {
                                ContractId = 0, // Will be updated via contract updates
                                TransactionId = 0 // Will be updated via contract updates
                            }
                        };
                        onComplete(successResponse);
                    }
                }
                else
                {
                    _logger.LogError($"[TIMING] EMERGENCY FAILURE: Não foi possível obter proposta para compra de emergência");

                    if (onComplete != null)
                    {
                        var errorResponse = new BuyResponse { Error = new ApiError { Message = "Emergency proposal failed" } };
                        onComplete(errorResponse);
                    }
                }
            }
            catch (Exception ex)
            {
                var errorTime = DateTimeOffset.Now;
                var errorDelay = (errorTime - instantStartTime).TotalMilliseconds;
                _logger.LogError(ex, $"[TIMING] INSTANT POOL BUY: Erro após {errorDelay}ms");
                
                if (onComplete != null)
                {
                    var errorResponse = new BuyResponse { Error = new ApiError { Message = ex.Message } };
                    onComplete(errorResponse);
                }
            }
        });
    }
    
    // Ultra-fast proposal method optimized for instant reuse
    private async Task<ProposalResponse> GetHotProposalAsync(ProposalRequest request)
    {
        try
        {
            // Create proposal request with minimal overhead
            var proposalRequest = new Dictionary<string, object>
            {
                ["proposal"] = 1,
                ["contract_type"] = request.ContractType,
                ["symbol"] = request.Symbol,
                ["duration"] = request.Duration,
                ["duration_unit"] = request.DurationUnit,
                ["currency"] = request.Currency,
                ["basis"] = request.Basis,
                ["amount"] = request.Stake
            };

            // Add optional parameters
            if (!string.IsNullOrEmpty(request.Barrier))
                proposalRequest["barrier"] = request.Barrier;
                
            if (!string.IsNullOrEmpty(request.Barrier2))
                proposalRequest["barrier2"] = request.Barrier2;
                
            if (request.LastDigitPrediction.HasValue)
                proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

            // Use ultra-fast request with reduced timeout for hot proposals
            var response = await SendFastRequestAsync(proposalRequest);
            var proposalResponse = response.Deserialize<ProposalResponse>();
            return proposalResponse ?? new ProposalResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter hot proposal");
            return new ProposalResponse { Error = new ProposalError { Message = ex.Message } };
        }
    }
}