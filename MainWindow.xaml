<Window x:Class="Excalibur.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Excalibur"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        xmlns:infrastructure="clr-namespace:Excalibur.Infrastructure"
        mc:Ignorable="d"
        Title="Excalibur - Deriv Trading Bot" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="#FF1E1E1E">
    
    <Window.Resources>
        <infrastructure:IsNotNullConverter x:Key="IsNotNullConverter" />
        <infrastructure:BoolToConnectionTextConverter x:Key="BoolToConnectionTextConverter" />
        <infrastructure:DecimalConverter x:Key="DecimalConverter" />
        <BooleanToVisibilityConverter x:Key="BoolToVis"/>
        
        <Style x:Key="PulseAnimation" TargetType="Ellipse">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsConnected}" Value="True">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard RepeatBehavior="Forever">
                                <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                                               From="1.0" To="0.3" Duration="0:0:1" 
                                               AutoReverse="True"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </DataTrigger.EnterActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        
        <!-- Estilo para botões com cantos arredondados e efeitos -->
        <Style x:Key="RoundedButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" Background="{TemplateBinding Background}" 
                                CornerRadius="6" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Border.Effect>
                                <DropShadowEffect x:Name="GlowEffect" Color="#2ECC71" BlurRadius="0" ShadowDepth="0" Opacity="0"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="GlowEffect" 
                                                           Storyboard.TargetProperty="BlurRadius" 
                                                           To="15" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="GlowEffect" 
                                                           Storyboard.TargetProperty="Opacity" 
                                                           To="0.8" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="GlowEffect" 
                                                           Storyboard.TargetProperty="BlurRadius" 
                                                           To="0" Duration="0:0:0.2"/>
                                            <DoubleAnimation Storyboard.TargetName="GlowEffect" 
                                                           Storyboard.TargetProperty="Opacity" 
                                                           To="0" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder" 
                                                          Storyboard.TargetProperty="Background.Color" 
                                                          To="#FF1E8449" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="ButtonBorder" 
                                                          Storyboard.TargetProperty="Background.Color" 
                                                          To="#FF2ECC71" Duration="0:0:0.2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Estilo para comboboxes com cantos arredondados e fonte azul -->
        <Style x:Key="RoundedComboBoxStyle" TargetType="ComboBox">
            <Style.Resources>
                <Style TargetType="ComboBoxItem">
                    <Setter Property="Foreground" Value="#FF4A90E2"/>
                </Style>
            </Style.Resources>
            <Setter Property="Foreground" Value="#FF4A90E2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <ToggleButton Name="ToggleButton" 
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Focusable="False"
                                        IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                        ClickMode="Press">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="6" 
                                                BorderBrush="{TemplateBinding BorderBrush}" 
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="20" />
                                                </Grid.ColumnDefinitions>
                                                <Path Grid.Column="1" 
                                                      HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center" 
                                                      Data="M 0 0 L 4 4 L 8 0 Z" 
                                                      Fill="#FFAAAAAA"/>
                                            </Grid>
                                        </Border>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>
                            <ContentPresenter Name="ContentSite" 
                                            IsHitTestVisible="False" 
                                            Content="{TemplateBinding SelectionBoxItem}" 
                                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" 
                                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" 
                                            Margin="6,3,23,3" 
                                            VerticalAlignment="Center" 
                                            HorizontalAlignment="Left"
                                            TextElement.Foreground="#FF4A90E2"/>
                            <TextBox x:Name="PART_EditableTextBox" 
                                   Style="{x:Null}" 
                                   Template="{DynamicResource ComboBoxTextBox}" 
                                   HorizontalAlignment="Left" 
                                   VerticalAlignment="Center" 
                                   Margin="3,3,23,3" 
                                   Focusable="True" 
                                   Background="Transparent" 
                                   Visibility="Hidden" 
                                   IsReadOnly="{TemplateBinding IsReadOnly}"/>
                            <Popup Name="Popup" 
                                 Placement="Bottom" 
                                 IsOpen="{TemplateBinding IsDropDownOpen}" 
                                 AllowsTransparency="True" 
                                 Focusable="False" 
                                 PopupAnimation="Slide">
                                <Grid Name="DropDown" 
                                    SnapsToDevicePixels="True" 
                                    MinWidth="{TemplateBinding ActualWidth}" 
                                    MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    <Border x:Name="DropDownBorder" 
                                          Background="#FF3E3E42" 
                                          BorderThickness="1" 
                                          BorderBrush="#FF5A5A5A" 
                                          CornerRadius="6"/>
                                    <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                        <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                    </ScrollViewer>
                                </Grid>
                            </Popup>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Estilo para TextBox com cantos arredondados -->
        <Style x:Key="RoundedTextBoxStyle" TargetType="TextBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Focusable="False" 
                                        HorizontalScrollBarVisibility="Hidden" 
                                        VerticalScrollBarVisibility="Hidden"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid Margin="10">
        <!-- Card de Status Compacto no Canto Superior Esquerdo -->
        <Border Background="#FF2D2D30" CornerRadius="6" Padding="12" 
                HorizontalAlignment="Left" VerticalAlignment="Top" 
                Width="250">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Informações da Conta -->
                <StackPanel Grid.Column="0">
                    <!-- Código da Conta -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,2">
                        <TextBlock Text="Conta:" FontSize="11" FontWeight="Medium" 
                                   Margin="0,0,8,0" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding AccountCode}" FontSize="11" 
                                   Foreground="White"/>
                    </StackPanel>
                    
                    <!-- Tipo da Conta -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,2">
                        <TextBlock Text="Tipo:" FontSize="11" FontWeight="Medium" 
                                   Margin="0,0,8,0" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding AccountType}" FontSize="11" 
                                   Foreground="White"/>
                    </StackPanel>
                    
                    <!-- Saldo -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,0">
                        <TextBlock Text="Saldo:" FontSize="11" FontWeight="Medium" 
                                   Margin="0,0,8,0" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding Balance, StringFormat='{}{0:C}'}" 
                                   FontSize="11" Foreground="#FF2ECC71" FontWeight="Medium"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- Status da Conexão e Ping no Canto Superior Direito -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Top">
                    <!-- Status da Conexão -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                        <Ellipse Width="10" Height="10" Margin="0,0,6,0" 
                                 Style="{StaticResource PulseAnimation}" Fill="#2ECC71"/>
                        <TextBlock FontSize="11" FontWeight="Medium" VerticalAlignment="Center" 
                                   Foreground="White">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Text" Value="Desconectado"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                            <Setter Property="Text" Value="Conectado"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                    
                    <!-- Ping -->
                    <TextBlock Text="{Binding Ping, StringFormat='{}{0} ms'}" 
                               FontSize="11" Foreground="White" 
                               HorizontalAlignment="Right"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Card de Seleção de Contratos -->
      <Border Background="#FF2D2D30" CornerRadius="6" Padding="12" 
                     HorizontalAlignment="Left" VerticalAlignment="Top" 
                     Width="250" Margin="0,75,0,0">
            <StackPanel>
                <TextBlock Text="Seleção de Contrato" FontWeight="Bold" FontSize="13" 
                           Margin="0,0,0,12" Foreground="White"/>
                
                <!-- Mercado -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="Mercado" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <ComboBox ItemsSource="{Binding Markets}" 
                               SelectedItem="{Binding SelectedMarket}" 
                               Width="160" HorizontalAlignment="Left"
                               Background="#FF3E3E42" Foreground="Black"
                               BorderBrush="#FF5A5A5A" FontSize="11"
                               Style="{StaticResource RoundedComboBoxStyle}"/>
                </StackPanel>
                
                <!-- Submercado -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="Submercado" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <ComboBox ItemsSource="{Binding SubMarkets}" 
                               SelectedItem="{Binding SelectedSubMarket}" 
                               IsEnabled="{Binding SelectedMarket, Converter={StaticResource IsNotNullConverter}}"
                               Width="226" HorizontalAlignment="Left"
                               Background="#FF3E3E42" Foreground="Black"
                               BorderBrush="#FF5A5A5A" FontSize="11"
                               Style="{StaticResource RoundedComboBoxStyle}"/>
                </StackPanel>
                
                <!-- Ativo -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="Ativo" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <ComboBox ItemsSource="{Binding ActiveSymbols}" 
                               SelectedItem="{Binding SelectedActiveSymbol}" 
                               DisplayMemberPath="DisplayName"
                               IsEnabled="{Binding SelectedSubMarket, Converter={StaticResource IsNotNullConverter}}"
                               Width="226" HorizontalAlignment="Left"
                               Background="#FF3E3E42" Foreground="Black"
                               BorderBrush="#FF5A5A5A" FontSize="11"
                               Style="{StaticResource RoundedComboBoxStyle}"/>
                </StackPanel>
                
                <!-- Tipo de Contrato -->
                <StackPanel Margin="0,0,0,0">
                    <TextBlock Text="Tipo de Contrato" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <ComboBox ItemsSource="{Binding ContractTypes}" 
                               SelectedItem="{Binding SelectedContractType}" 
                               IsEnabled="{Binding SelectedActiveSymbol, Converter={StaticResource IsNotNullConverter}}"
                               Width="226" HorizontalAlignment="Left"
                               Background="#FF3E3E42" Foreground="Black"
                               BorderBrush="#FF5A5A5A" FontSize="11"
                               Style="{StaticResource RoundedComboBoxStyle}">
                         <ComboBox.ItemTemplate>
                             <DataTemplate>
                                 <StackPanel>
                                     <TextBlock Text="{Binding CategoryDisplay}" FontWeight="SemiBold"/>
                                     <TextBlock Text="{Binding ContractDisplay}" FontSize="11" Opacity="0.8"/>
                                 </StackPanel>
                             </DataTemplate>
                         </ComboBox.ItemTemplate>
                     </ComboBox>
                </StackPanel>
            </StackPanel>
        </Border>
        
        <!-- Card de Money Management -->
        <Border Background="#FF2D2D30" CornerRadius="6" Padding="12" 
                HorizontalAlignment="Left" VerticalAlignment="Top" 
                Width="250" Margin="253,75,0,0">
            <StackPanel>
                <TextBlock Text="Money Management" FontWeight="Bold" FontSize="13" 
                           Margin="0,0,0,12" Foreground="White"/>
                
                <!-- RadioButtons para seleção do tipo de gerenciamento -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                    <RadioButton Name="NoneRadioButton" Content="Nenhum" 
                                 FontSize="11" Foreground="White" 
                                 Margin="0,0,20,0" GroupName="MoneyManagement"
                                 IsChecked="{Binding IsNoneSelected}">
                        <RadioButton.Resources>
                            <Style TargetType="RadioButton">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="RadioButton">
                                            <StackPanel Orientation="Horizontal">
                                                <Ellipse Width="12" Height="12" 
                                         Stroke="#FFCCCCCC" StrokeThickness="1" 
                                         Margin="0,0,6,0">
                                                    <Ellipse.Style>
                                                        <Style TargetType="Ellipse">
                                                            <Setter Property="Fill" Value="Transparent"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsChecked, RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                                                                    <Setter Property="Fill" Value="#FFFF00"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Ellipse.Style>
                                                </Ellipse>
                                                <ContentPresenter VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </RadioButton.Resources>
                    </RadioButton>
                    
                    <RadioButton Name="MartingaleRadioButton" Content="Martingale" 
                                 FontSize="11" Foreground="White" 
                                 GroupName="MoneyManagement"
                                 IsChecked="{Binding IsMartingaleEnabled}">
                        <RadioButton.Resources>
                            <Style TargetType="RadioButton">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="RadioButton">
                                            <StackPanel Orientation="Horizontal">
                                                <Ellipse Width="12" Height="12" 
                                         Stroke="#FFCCCCCC" StrokeThickness="1" 
                                         Margin="0,0,6,0">
                                                    <Ellipse.Style>
                                                        <Style TargetType="Ellipse">
                                                            <Setter Property="Fill" Value="Transparent"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsChecked, RelativeSource={RelativeSource TemplatedParent}}" Value="True">
                                                                    <Setter Property="Fill" Value="#FFFF00"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Ellipse.Style>
                                                </Ellipse>
                                                <ContentPresenter VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </RadioButton.Resources>
                    </RadioButton>
                </StackPanel>
                
                <!-- Campos do Martingale (visíveis apenas quando selecionado) -->
                <StackPanel Visibility="{Binding IsMartingaleEnabled, Converter={StaticResource BoolToVis}}">
                    <!-- Factor e Level na mesma linha -->
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="110"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Factor -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Factor" FontWeight="Medium" FontSize="11" 
                                       Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                            <TextBox Text="{Binding MartingaleFactor, UpdateSourceTrigger=LostFocus, Converter={StaticResource DecimalConverter}, Mode=TwoWay}" 
                                     Height="32" VerticalContentAlignment="Center"
                                     Background="#FF3E3E42" Foreground="White"
                                     BorderBrush="#FF5A5A5A" FontSize="11"
                                     Style="{StaticResource RoundedTextBoxStyle}"/>
                        </StackPanel>
                        
                        <!-- Level -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Level" FontWeight="Medium" FontSize="11" 
                                       Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                            <TextBox Text="{Binding MartingaleLevel, UpdateSourceTrigger=PropertyChanged}" 
                                     Height="32" VerticalContentAlignment="Center"
                                     Background="#FF3E3E42" Foreground="White"
                                     BorderBrush="#FF5A5A5A" FontSize="11"
                                     Style="{StaticResource RoundedTextBoxStyle}"/>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Toggle Fast -->
                    <StackPanel Margin="0,0,0,12">
                        <CheckBox Name="FastToggle" Content="Fast" 
                                  FontSize="11" Foreground="White"
                                  IsChecked="{Binding IsFastMartingale}"/>
                    </StackPanel>
                    
                    <!-- Próxima Stake no canto inferior esquerdo -->
                    <StackPanel HorizontalAlignment="Left" VerticalAlignment="Bottom">
                        <TextBlock FontSize="11" Foreground="#FFAAAAAA">
                            <Run Text="Próxima Stake: "/>
                            <Run Text="{Binding NextStakeAmount, StringFormat='{}{0:F2}'}" Foreground="#FFFF6B35"/>
                        </TextBlock>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
        </Border>
        
        <!-- Card de Parâmetros do Contrato -->
        <Border Background="#FF2D2D30" CornerRadius="6" Padding="12" 
                HorizontalAlignment="Left" VerticalAlignment="Top" 
                Width="250" Margin="0,320,0,0">
            <StackPanel>
                
                <TextBlock Text="Parâmetros do Contrato" FontWeight="Bold" FontSize="13" 
                           Margin="0,0,0,12" Foreground="White"/>
                
                <!-- Campo de Duração -->
                <StackPanel Visibility="{Binding IsDurationVisible, Converter={StaticResource BoolToVis}}">
                    <TextBlock Text="Duração" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <TextBlock Text="{Binding DurationInfo}" FontSize="10" Opacity="0.8" 
                               Margin="0,0,0,5" Foreground="#FFAAAAAA"/>
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="5"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Text="{Binding DurationValue, UpdateSourceTrigger=PropertyChanged}" 
                                 Height="32" VerticalContentAlignment="Center"
                                 Background="#FF3E3E42" Foreground="White"
                                 BorderBrush="#FF5A5A5A" FontSize="11"
                                 Style="{StaticResource RoundedTextBoxStyle}"/>
                        <ComboBox Grid.Column="2" Width="80" HorizontalAlignment="Left"
                                  Height="32"
                                  SelectedValue="{Binding DurationUnit}"
                                  SelectedValuePath="Tag"
                                  Background="#FF3E3E42" Foreground="Black"
                                  BorderBrush="#FF5A5A5A" FontSize="11"
                                  Style="{StaticResource RoundedComboBoxStyle}">
                            <ComboBoxItem Content="Ticks" Tag="t"/>
                            <ComboBoxItem Content="Segundos" Tag="s"/>
                            <ComboBoxItem Content="Minutos" Tag="m"/>
                            <ComboBoxItem Content="Horas" Tag="h"/>
                            <ComboBoxItem Content="Dias" Tag="d"/>
                        </ComboBox>
                    </Grid>
                </StackPanel>

                <!-- Campo de Barreira 1 -->
                <StackPanel Margin="0,0,0,8" Visibility="{Binding IsBarrier1Visible, Converter={StaticResource BoolToVis}}">
                    <TextBlock Text="Barreira 1" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <TextBlock Text="{Binding Barrier1Suggestion}" FontSize="10" Opacity="0.8" 
                               Margin="0,0,0,5" Foreground="#FFAAAAAA"/>
                    <TextBox Text="{Binding Barrier1Value, UpdateSourceTrigger=PropertyChanged}" Width="100" HorizontalAlignment="Left"
                             Background="#FF3E3E42" Foreground="White"
                             BorderBrush="#FF5A5A5A" FontSize="11"
                             Style="{StaticResource RoundedTextBoxStyle}"/>
                </StackPanel>
                
                <!-- Campo de Barreira 2 -->
                <StackPanel Margin="0,0,0,8" Visibility="{Binding IsBarrier2Visible, Converter={StaticResource BoolToVis}}">
                    <TextBlock Text="Barreira 2" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <TextBlock Text="{Binding Barrier2Suggestion}" FontSize="10" Opacity="0.8" 
                               Margin="0,0,0,5" Foreground="#FFAAAAAA"/>
                    <TextBox Text="{Binding Barrier2Value, UpdateSourceTrigger=PropertyChanged}" Width="100" HorizontalAlignment="Left"
                             Background="#FF3E3E42" Foreground="White"
                             BorderBrush="#FF5A5A5A" FontSize="11"
                             Style="{StaticResource RoundedTextBoxStyle}"/>
                </StackPanel>
                
                <!-- Campo de Seleção de Dígito -->
                <StackPanel Margin="0,0,0,8" Visibility="{Binding IsDigitSelectionVisible, Converter={StaticResource BoolToVis}}">
                    <TextBlock Text="Previsão do Último Dígito" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <ComboBox SelectedValue="{Binding SelectedDigit}" 
                              SelectedValuePath="Tag"
                              Width="226" HorizontalAlignment="Left"
                              Background="#FF3E3E42" Foreground="Black"
                              BorderBrush="#FF5A5A5A" FontSize="11"
                              Style="{StaticResource RoundedComboBoxStyle}">
                        <ComboBoxItem Content="0" Tag="0"/>
                        <ComboBoxItem Content="1" Tag="1"/>
                        <ComboBoxItem Content="2" Tag="2"/>
                        <ComboBoxItem Content="3" Tag="3"/>
                        <ComboBoxItem Content="4" Tag="4"/>
                        <ComboBoxItem Content="5" Tag="5"/>
                        <ComboBoxItem Content="6" Tag="6"/>
                        <ComboBoxItem Content="7" Tag="7"/>
                        <ComboBoxItem Content="8" Tag="8"/>
                        <ComboBoxItem Content="9" Tag="9"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- Campo Stake com Botão Buy -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="Stake" FontWeight="Medium" FontSize="11" 
                               Margin="0,0,0,4" Foreground="#FFAAAAAA"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="5"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Text="{Binding StakeAmount, UpdateSourceTrigger=PropertyChanged}" 
                                 Height="32" VerticalContentAlignment="Center"
                                 Background="#FF3E3E42" Foreground="White"
                                 BorderBrush="#FF5A5A5A" FontSize="11"
                                 Style="{StaticResource RoundedTextBoxStyle}"/>
                        <Button Grid.Column="2" Content="Buy" Height="32" Width="120"
                                Background="#FF2ECC71" Foreground="White" FontWeight="Bold"
                                BorderThickness="0" FontSize="11"
                                Command="{Binding BuyCommand}"
                                Style="{StaticResource RoundedButtonStyle}"/>
                    </Grid>
                </StackPanel>
                
                <Separator Margin="0,15,0,10" Background="#FF5A5A5A"/>
                
                <!-- Resultados do Cálculo -->
                <StackPanel>
                    <TextBlock Text="Proposta" FontWeight="Bold" FontSize="12" 
                               Margin="0,0,0,8" Foreground="White"/>
                    
                    <!-- Indicador de Cálculo -->
                    <TextBlock Text="Calculando..." FontSize="10" 
                               Margin="0,0,0,5" Foreground="#FFAAAAAA"
                               Visibility="{Binding IsCalculating, Converter={StaticResource BoolToVis}}"/>
                    
                    <!-- Payout -->
                    <StackPanel Margin="0,0,0,5">
                        <TextBlock Text="Payout:" FontSize="10" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding CalculatedPayout, StringFormat='{}{0:F2}'}" 
                                   FontSize="12" FontWeight="Medium" Foreground="#FF2ECC71"/>
                    </StackPanel>
                    
                    <!-- Preço de Compra -->
                    <StackPanel Margin="0,0,0,5">
                        <TextBlock Text="Preço de Compra:" FontSize="10" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding AskPrice, StringFormat='{}{0:F2}'}" 
                                   FontSize="12" FontWeight="Medium" Foreground="#FFFF6B35"/>
                    </StackPanel>
                    
                    <!-- Barreiras Calculadas -->
                    <StackPanel Margin="0,0,0,5" Visibility="{Binding IsBarrier1Visible, Converter={StaticResource BoolToVis}}">
                        <TextBlock Text="Barreira Calculada:" FontSize="10" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding CalculatedBarrier1, StringFormat='{}{0:F5}'}" 
                                   FontSize="11" Foreground="White"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,0,0,5" Visibility="{Binding IsBarrier2Visible, Converter={StaticResource BoolToVis}}">
                        <TextBlock Text="Barreira 2 Calculada:" FontSize="10" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding CalculatedBarrier2, StringFormat='{}{0:F5}'}" 
                                   FontSize="11" Foreground="White"/>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
