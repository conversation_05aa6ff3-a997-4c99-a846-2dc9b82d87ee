using System.Text.Json.Serialization;

namespace Excalibur.Models;

public class ActiveSymbol
{
    [JsonPropertyName("display_name")]
    public string DisplayName { get; set; } = string.Empty;

    [JsonPropertyName("market")]
    public string Market { get; set; } = string.Empty;

    [JsonPropertyName("market_display_name")]
    public string MarketDisplayName { get; set; } = string.Empty;

    [JsonPropertyName("submarket")]
    public string Submarket { get; set; } = string.Empty;

    [JsonPropertyName("submarket_display_name")]
    public string SubmarketDisplayName { get; set; } = string.Empty;

    [JsonPropertyName("symbol")]
    public string Symbol { get; set; } = string.Empty;
}