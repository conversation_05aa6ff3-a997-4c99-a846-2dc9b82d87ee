Vamos seguir os passos para construir a base da sua aplicação.
Estrutura do Projeto
Para seguir os princípios SOLID, organizaremos o projeto da seguinte forma:
Excalibur/
Models/: Classes que representam os dados da aplicação (ex: AccountInfo).
ViewModels/: Classes que contêm a lógica da interface e o estado da View (ex: MainViewModel).
Views/: Os arquivos XAML e code-behind que definem a interface do usuário (ex: MainWindow.xaml).
Services/: Classes responsáveis pela lógica de negócio, como a comunicação com a API da Deriv (ex: DerivApiService).
Infrastructure/: Classes de apoio, como conversores de binding, helpers, etc.
Passo 1: Criação do Projeto e Instalação de Pacotes
Abra o Visual Studio ou use a linha de comando para criar um novo projeto WPF:
code
Bash
dotnet new wpf -n Excalibur
cd Excalibur
Instale os pacotes NuGet necessários:
WPF UI: Para os componentes visuais modernos.
Websocket.Client: Uma biblioteca robusta e com reconexão automática para lidar com a API WebSocket da Deriv.
Microsoft.Extensions.Hosting: Para injeção de dependência, que facilita a aplicação dos princípios SOLID.
Serilog: Para um sistema de logging flexível.
Execute os seguintes comandos no terminal do seu projeto:
code
Bash
dotnet add package Wpf.Ui
dotnet add package Websocket.Client
dotnet add package Microsoft.Extensions.Hosting
dotnet add package Serilog.Extensions.Hosting
dotnet add package Serilog.Sinks.File
Passo 2: Configurando a Injeção de Dependência (App.xaml.cs)
Vamos configurar a aplicação para usar injeção de dependência. Isso permite que nosso MainViewModel receba o serviço da API sem precisar criá-lo, o que é ótimo para desacoplamento e testes.
App.xaml.cs
code
C#
using Excalibur.Services;
using Excalibur.ViewModels;
using Excalibur.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Windows;

public partial class App : Application
{
    private static readonly IHost _host = Host
        .CreateDefaultBuilder()
        .UseSerilog((context, config) =>
        {
            config.WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day);
        })
        .ConfigureServices((context, services) =>
        {
            // Registrar Serviços
            services.AddSingleton<IDerivApiService, DerivApiService>();

            // Registrar ViewModels
            services.AddSingleton<MainViewModel>();

            // Registrar a Janela Principal
            services.AddSingleton<MainWindow>();
        })
        .Build();

    protected override async void OnStartup(StartupEventArgs e)
    {
        await _host.StartAsync();

        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.DataContext = _host.Services.GetRequiredService<MainViewModel>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        await _host.StopAsync();
        base.OnExit(e);
    }
}
App.xaml
Modifique o App.xaml para remover StartupUri="MainWindow.xaml", pois vamos controlar a inicialização da janela via código.
code
Xml
<Application x:Class="Excalibur.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:Excalibur">
    <Application.Resources>
        <!-- Importando os estilos do WPF UI -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ui:ThemesDictionary Theme="Dark" xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml" />
                <ui:ControlsDictionary xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
Passo 3: O Serviço da API (DerivApiService.cs)
Este serviço será o coração da comunicação com a Deriv. Ele vai gerenciar a conexão WebSocket, autenticação, reconexão automática, envio de pings e o log de operações.
Crie uma interface IDerivApiService.cs na pasta Services:
code
C#
using System;
using System.Threading.Tasks;

namespace Excalibur.Services;

public interface IDerivApiService
{
    bool IsConnected { get; }
    event Action<string, string, double> AccountInfoUpdated;
    event Action<long> PingUpdated;
    event Action ConnectionEstablished;
    event Action ConnectionLost;

    Task ConnectAndAuthorizeAsync();
}
Agora, a implementação DerivApiService.cs:
code
C#
using System;
using System.Diagnostics;
using System.Net.WebSockets;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Websocket.Client;

namespace Excalibur.Services;

public class DerivApiService : IDerivApiService
{
    private readonly ILogger<DerivApiService> _logger;
    private IWebsocketClient _ws;
    private Timer _pingTimer;
    private readonly Stopwatch _pingStopwatch = new();

    // Configurações - virão da UI no futuro
    private string _apiToken = "oLJLFtINRDBGUh1";
    private int _appId = 82663;

    public bool IsConnected => _ws?.IsRunning ?? false;

    public event Action<string, string, double> AccountInfoUpdated;
    public event Action<long> PingUpdated;
    public event Action ConnectionEstablished;
    public event Action ConnectionLost;

    public DerivApiService(ILogger<DerivApiService> logger)
    {
        _logger = logger;
        InitializeClient();
    }

    private void InitializeClient()
    {
        var url = new Uri($"wss://ws.binaryws.com/websockets/v3?app_id={_appId}");
        _ws = new WebsocketClient(url);

        _ws.ReconnectTimeout = TimeSpan.FromSeconds(30); // Tempo para tentar reconectar
        _ws.ErrorReconnectTimeout = TimeSpan.FromSeconds(30);

        _ws.ReconnectionHappened.Subscribe(info => {
            _logger.LogInformation($"Reconexão bem-sucedida: {info.Type}");
            Authorize();
            ConnectionEstablished?.Invoke();
        });

        _ws.DisconnectionHappened.Subscribe(info => {
            _logger.LogWarning($"Conexão perdida: {info.Type}");
            _pingTimer?.Dispose();
            ConnectionLost?.Invoke();
        });

        _ws.MessageReceived.Subscribe(msg => {
            _logger.LogInformation($"Mensagem recebida: {msg.Text}");
            ProcessMessage(msg.Text);
        });
    }

    public async Task ConnectAndAuthorizeAsync()
    {
        _logger.LogInformation("Conectando à API Deriv...");
        await _ws.Start();
        if (_ws.IsRunning)
        {
            Authorize();
            ConnectionEstablished?.Invoke();
        }
    }

    private void Authorize()
    {
        var authRequest = new { authorize = _apiToken };
        _ws.Send(JsonSerializer.Serialize(authRequest));
        _logger.LogInformation("Pedido de autorização enviado.");
    }
    
    private void StartPingTimer()
    {
        _pingTimer = new Timer(_ => SendPing(), null, 0, 5000); // Envia ping a cada 5 segundos
    }

    private void SendPing()
    {
        _pingStopwatch.Restart();
        var pingRequest = new { ping = 1 };
        _ws.Send(JsonSerializer.Serialize(pingRequest));
    }

    private void ProcessMessage(string jsonMessage)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonMessage);
            var root = doc.RootElement;
            var msgType = root.GetProperty("msg_type").GetString();

            switch (msgType)
            {
                case "authorize":
                    if (root.TryGetProperty("error", out var error))
                    {
                        _logger.LogError($"Erro de autorização: {error.GetProperty("message").GetString()}");
                        return;
                    }

                    var authResponse = root.GetProperty("authorize");
                    var loginid = authResponse.GetProperty("loginid").GetString();
                    var accountType = authResponse.GetProperty("is_virtual").GetInt32() == 1 ? "Virtual" : "Real";
                    var balance = authResponse.GetProperty("balance").GetDouble();
                    AccountInfoUpdated?.Invoke(loginid, accountType, balance);
                    
                    // Inicia o timer de ping após autorização bem-sucedida
                    StartPingTimer();
                    break;
                
                case "balance":
                    var balanceResponse = root.GetProperty("balance");
                    var updatedBalance = balanceResponse.GetProperty("balance").GetDouble();
                    var loginIdBalance = balanceResponse.GetProperty("loginid").GetString();
                    // Assumindo que o tipo de conta não muda
                    AccountInfoUpdated?.Invoke(loginIdBalance, null, updatedBalance);
                    break;
                    
                case "pong":
                    _pingStopwatch.Stop();
                    PingUpdated?.Invoke(_pingStopwatch.ElapsedMilliseconds);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem da API.");
        }
    }
}
Passo 4: O ViewModel (MainViewModel.cs)
Este ViewModel vai expor os dados do serviço para a View (interface gráfica) e lidar com a lógica de apresentação.
Primeiro, crie uma classe base para INotifyPropertyChanged para evitar repetição de código. Na pasta ViewModels, crie ObservableObject.cs:
code
C#
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Excalibur.ViewModels;

public class ObservableObject : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler? PropertyChanged;

    protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
Agora, o MainViewModel.cs:
code
C#
using Excalibur.Services;
using System;
using System.Windows;

namespace Excalibur.ViewModels;

public class MainViewModel : ObservableObject
{
    private readonly IDerivApiService _derivApiService;

    private bool _isConnected;
    public bool IsConnected
    {
        get => _isConnected;
        set { _isConnected = value; OnPropertyChanged(); }
    }

    private string _accountCode = "-----------";
    public string AccountCode
    {
        get => _accountCode;
        set { _accountCode = value; OnPropertyChanged(); }
    }
    
    private string _accountType = "---";
    public string AccountType
    {
        get => _accountType;
        set { _accountType = value; OnPropertyChanged(); }
    }

    private double _balance;
    public double Balance
    {
        get => _balance;
        set { _balance = value; OnPropertyChanged(); }
    }

    private long _ping;
    public long Ping
    {
        get => _ping;
        set { _ping = value; OnPropertyChanged(); }
    }
    
    public MainViewModel(IDerivApiService derivApiService)
    {
        _derivApiService = derivApiService;
        SubscribeToApiEvents();
        _derivApiService.ConnectAndAuthorizeAsync();
    }

    private void SubscribeToApiEvents()
    {
        _derivApiService.ConnectionEstablished += OnConnectionEstablished;
        _derivApiService.ConnectionLost += OnConnectionLost;
        _derivApiService.AccountInfoUpdated += OnAccountInfoUpdated;
        _derivApiService.PingUpdated += OnPingUpdated;
    }

    private void OnPingUpdated(long newPing)
    {
        Application.Current.Dispatcher.Invoke(() => Ping = newPing);
    }

    private void OnAccountInfoUpdated(string accountCode, string accountType, double balance)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            AccountCode = accountCode;
            // O tipo de conta só vem na autorização, então não atualizamos se for nulo
            if (accountType != null) 
            {
                AccountType = accountType;
            }
            Balance = balance;
        });
    }

    private void OnConnectionLost()
    {
        Application.Current.Dispatcher.Invoke(() => IsConnected = false);
    }

    private void OnConnectionEstablished()
    {
        Application.Current.Dispatcher.Invoke(() => IsConnected = true);
    }
}
Passo 5: A View (MainWindow.xaml)
Esta é a parte visual. Vamos usar componentes do WPF UI para criar o card de status com o efeito pulsante.
MainWindow.xaml
code
Xml
<Window x:Class="Excalibur.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Excalibur.Views"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        mc:Ignorable="d"
        Title="Excalibur Trader" Height="450" Width="800"
        WindowStartupLocation="CenterScreen"
        ExtendsContentIntoTitleBar="True" 
        WindowBackdropType="Mica">

    <Window.Resources>
        <!-- Estilo para o efeito de pulso -->
        <Style x:Key="PulsingDotStyle" TargetType="Grid">
            <Setter Property="Visibility" Value="Collapsed"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsConnected}" Value="True">
                    <Setter Property="Visibility" Value="Visible"/>
                    <DataTrigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetName="pulseEllipse"
                                                 Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                 From="1" To="3" Duration="0:0:1.5"
                                                 RepeatBehavior="Forever" AutoReverse="False"/>
                                <DoubleAnimation Storyboard.TargetName="pulseEllipse"
                                                 Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                 From="1" To="3" Duration="0:0:1.5"
                                                 RepeatBehavior="Forever" AutoReverse="False"/>
                                <DoubleAnimation Storyboard.TargetName="pulseEllipse"
                                                 Storyboard.TargetProperty="Opacity"
                                                 From="0.5" To="0" Duration="0:0:1.5"
                                                 RepeatBehavior="Forever" AutoReverse="False"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </DataTrigger.EnterActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid>
        <ui:CardControl Header="Status da Conexão"
                        Margin="20"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        Width="300">
            <StackPanel Margin="10">
                <!-- Informações da Conta -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                    </Grid.RowDefinitions>

                    <ui:SymbolIcon Grid.Row="0" Symbol="Contact24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding AccountCode}" FontWeight="SemiBold" VerticalAlignment="Center"/>
                    
                    <ui:SymbolIcon Grid.Row="1" Symbol="Wallet24" Margin="0,5,10,0" VerticalAlignment="Center"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Margin="0,5,0,0" VerticalAlignment="Center">
                        <Run Text="{Binding AccountType, FallbackValue='-'}"/>: 
                        <Run Text="{Binding Balance, StringFormat='C', FallbackValue='$0.00'}" FontWeight="Bold"/>
                    </TextBlock>
                </Grid>

                <Separator Margin="0,15"/>
                
                <!-- Status da Conexão e Ping -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Bolinha Verde com Efeito -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <Grid Width="16" Height="16" Margin="0,0,8,0">
                            <!-- Efeito de pulso -->
                            <Grid Style="{StaticResource PulsingDotStyle}">
                                <Ellipse x:Name="pulseEllipse" Fill="#2ECC71" Opacity="0.5">
                                    <Ellipse.RenderTransform>
                                        <ScaleTransform CenterX="8" CenterY="8"/>
                                    </Ellipse.RenderTransform>
                                </Ellipse>
                            </Grid>
                            <!-- Ponto central fixo -->
                            <Ellipse Fill="#2ECC71" Width="8" Height="8" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="Conectado" FontWeight="Bold" Foreground="#2ECC71" VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Informação do Ping -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="Clock24" Margin="0,0,5,0"/>
                        <TextBlock>
                            <Run Text="Ping:"/>
                            <Run Text="{Binding Ping, FallbackValue='--'}"/>
                            <Run Text="ms"/>
                        </TextBlock>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </ui:CardControl>
    </Grid>
</Window>
MainWindow.xaml.cs (code-behind)
O code-behind fica bem simples, pois a lógica está no ViewModel.
code
C#
using System.Windows;

namespace Excalibur.Views;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
    }
}
Resumo e Próximos Passos
Para executar o projeto:
Garanta que todos os arquivos foram criados nas pastas corretas.
Compile e execute o projeto a partir do Visual Studio ou com dotnet run.
Uma janela com o título "Excalibur Trader" aparecerá.
No canto superior esquerdo, você verá o card "Status da Conexão".
Em instantes, ele deverá se conectar à API da Deriv:
A bolinha verde aparecerá com o efeito de pulso.
O Código da Conta, Tipo (Virtual/Real) e Saldo serão preenchidos.
O Ping será atualizado a cada 5 segundos.
Verifique a pasta logs no seu diretório de execução para ver os logs de conexão e mensagens.
Melhorias Futuras (Conforme sua solicitação):
Configurações de API: Crie uma nova View/ViewModel para uma tela de configurações onde o usuário possa inserir o Token e o App ID. Salve essas informações em um arquivo de configuração (appsettings.json ou nas configurações do usuário) e carregue-as no DerivApiService.
Log de Operações: O Serilog já está implementado. Você pode injetar o ILogger em outros serviços e ViewModels para registrar compras de contrato, expirações e outros eventos importantes.
Atualização Automática de Saldo: O DerivApiService já está preparado para isso. A API da Deriv envia uma mensagem do tipo balance quando o saldo muda. O case "balance": no ProcessMessage já captura isso e atualiza a UI. Você só precisa se inscrever para receber as transações (transaction stream) para ter atualizações em tempo real.image.png