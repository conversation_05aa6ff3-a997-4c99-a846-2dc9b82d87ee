using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Excalibur.Models;

namespace Excalibur.Services;

public interface IDerivApiService
{
    bool IsConnected { get; }
    event Action<string, string, double> AccountInfoUpdated;
    event Action<long> PingUpdated;
    event Action ConnectionEstablished;
    event Action ConnectionLost;
    event Action<bool> ContractResult; // true = win, false = loss
    event Action<string> ContractNearExpiry; // contract_id when contract is near expiry

    Task ConnectAndAuthorizeAsync();
    Task<List<ActiveSymbol>> GetActiveSymbolsAsync();
    Task<ContractsForSymbol> GetContractsForSymbolAsync(string symbol);
    Task<ProposalResponse> GetProposalAsync(ProposalRequest request);
    Task<ProposalResponse> GetFastProposalAsync(ProposalRequest request); // Ultra-low latency proposal for fast martingale
    Task<BuyResponse> BuyContractAsync(string proposalId, decimal price);
    void BuyContractImmediateAsync(string proposalId, decimal price, Action<BuyResponse> onComplete = null); // Same-time execution for fast martingale
    void GetFastProposalAndBuyAsync(ProposalRequest request, Action<ProposalResponse, BuyResponse> onComplete); // Zero-delay proposal + buy in one operation
    void BuyInstantMarketAsync(ProposalRequest request, Action<BuyResponse> onComplete = null); // INSTANT MARKET BUY: Uses hot proposal pool for true same-time execution
    Task SubscribeToPortfolioAsync();
    Task SubscribeToBalanceAsync();
}