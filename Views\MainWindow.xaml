<Window x:Class="Excalibur.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Excalibur.Views"
        mc:Ignorable="d"
        Title="Excalibur - Deriv API Monitor" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="#FF1E1E1E">

    <Window.Resources>
        <Style x:Key="PulseAnimation" TargetType="Ellipse">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsConnected}" Value="True">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard RepeatBehavior="Forever">
                                <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                                               From="1.0" To="0.3" Duration="0:0:1" 
                                               AutoReverse="True"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </DataTrigger.EnterActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="10">
        <!-- Card de Status Compacto no Canto Superior Esquerdo -->
        <Border Background="#FF2D2D30" CornerRadius="6" Padding="12" 
                HorizontalAlignment="Left" VerticalAlignment="Top" 
                Width="250">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Informações da Conta -->
                <StackPanel Grid.Column="0">
                    <!-- Código da Conta -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,2">
                        <TextBlock Text="Conta:" FontSize="11" FontWeight="Medium" 
                                   Margin="0,0,8,0" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding AccountCode}" FontSize="11" 
                                   Foreground="White"/>
                    </StackPanel>
                    
                    <!-- Tipo da Conta -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,2">
                        <TextBlock Text="Tipo:" FontSize="11" FontWeight="Medium" 
                                   Margin="0,0,8,0" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding AccountType}" FontSize="11" 
                                   Foreground="White"/>
                    </StackPanel>
                    
                    <!-- Saldo -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,0">
                        <TextBlock Text="Saldo:" FontSize="11" FontWeight="Medium" 
                                   Margin="0,0,8,0" Foreground="#FFAAAAAA"/>
                        <TextBlock Text="{Binding Balance, StringFormat='{}{0:C}'}" 
                                   FontSize="11" Foreground="#FF2ECC71" FontWeight="Medium"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- Status da Conexão e Ping no Canto Superior Direito -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Top">
                    <!-- Status da Conexão -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                        <Ellipse Width="10" Height="10" Margin="0,0,6,0" 
                                 Style="{StaticResource PulseAnimation}" Fill="#2ECC71"/>
                        <TextBlock FontSize="11" FontWeight="Medium" VerticalAlignment="Center" 
                                   Foreground="White">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Text" Value="Desconectado"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                            <Setter Property="Text" Value="Conectado"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                    
                    <!-- Ping -->
                    <TextBlock Text="{Binding Ping, StringFormat='{}{0} ms'}" 
                               FontSize="11" Foreground="White" 
                               HorizontalAlignment="Right"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Espaço reservado para futuros elementos -->
        <!-- O resto da tela fica disponível para outros componentes -->
    </Grid>
</Window>
